<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Auth;

class SimpleTrialRegistrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function guest_can_access_registration_form()
    {
        $response = $this->get(route('simple.event.trial.register.form'));
        
        $response->assertStatus(200);
        $response->assertViewIs('simple_event_trial_register');
    }

    /** @test */
    public function authenticated_user_is_redirected_from_registration_form()
    {
        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(route('simple.event.trial.register.form'));
        
        // Should be redirected to dashboard (HOME constant in RouteServiceProvider)
        $response->assertRedirect('/dashboard');
    }

    /** @test */
    public function guest_can_register_successfully()
    {
        $registrationData = [
            'full_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'mobile' => '**********',
        ];

        $response = $this->post(route('simple.event.trial.register'), $registrationData);

        // Should redirect to dashboard after successful registration
        $response->assertRedirect(route('dashboard'));
        $response->assertSessionHas('success');

        // User should be created and authenticated
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'username' => '<EMAIL>',
        ]);

        $this->assertTrue(Auth::check());
    }

    /** @test */
    public function authenticated_user_cannot_register_again()
    {
        // Create and authenticate a user
        $user = User::factory()->create();
        $this->actingAs($user);

        $registrationData = [
            'full_name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'mobile' => '**********',
        ];

        $response = $this->post(route('simple.event.trial.register'), $registrationData);

        // Should be redirected to dashboard (not process registration)
        $response->assertRedirect('/dashboard');

        // New user should NOT be created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function registration_validates_required_fields()
    {
        $response = $this->post(route('simple.event.trial.register'), []);

        $response->assertSessionHasErrors(['full_name', 'email', 'mobile']);
    }

    /** @test */
    public function registration_validates_unique_email()
    {
        // Create a user with existing email
        User::factory()->create(['email' => '<EMAIL>']);

        $registrationData = [
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '**********',
        ];

        $response = $this->post(route('simple.event.trial.register'), $registrationData);

        $response->assertSessionHasErrors(['email']);
    }
}
