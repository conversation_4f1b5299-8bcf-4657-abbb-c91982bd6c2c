<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .company-logo {
            margin-bottom: 10px;
        }

        .company-logo img {
            max-width: 120px;
            max-height: 80px;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }

        .company-details {
            font-size: 11px;
            margin: 2px 0;
        }

        .report-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            text-decoration: underline;
        }

        .details-section {
            float: right;
            width: 200px;
            margin-bottom: 20px;
        }

        .details-section table {
            width: 100%;
            font-size: 11px;
        }

        .details-section td {
            padding: 2px 5px;
            border: 1px solid #000;
        }

        .details-section .header-cell {
            background-color: #e0e0e0;
            font-weight: bold;
            text-align: center;
        }

        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
            clear: both;
        }

        .financial-table th {
            text-align: right;
            padding: 5px 10px;
            font-size: 11px;
            font-weight: bold;
            border-bottom: 1px solid #000;
        }

        .financial-table td {
            padding: 3px 10px;
            vertical-align: top;
            border: none;
        }

        .category-row {
            font-weight: bold;
            margin-top: 15px;
        }

        .category-row td {
            padding-top: 15px;
            border-top: 1px solid #ccc;
        }

        .subcategory-row td {
            padding-left: 20px;
            font-size: 10px;
        }

        .total-row {
            font-weight: bold;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
        }

        .total-row td {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        .amount-col {
            text-align: right;
            width: 80px;
            font-family: monospace;
        }

        .percentage-col {
            text-align: right;
            width: 60px;
            font-family: monospace;
        }

        .description-col {
            text-align: left;
            padding-left: 0;
        }

        .main-section {
            margin-top: 20px;
        }

        .profit-row {
            font-weight: bold;
            font-size: 12px;
        }

        .profit-row td {
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .final-profit-row {
            font-weight: bold;
            font-size: 12px;
            border-top: 2px solid #000;
            border-bottom: 2px solid #000;
        }

        .final-profit-row td {
            padding-top: 10px;
            padding-bottom: 10px;
        }

        @page {
            margin: 1.5cm;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        @if($companyLogo)
            <div class="company-logo">
                <img src="{{ $companyLogo }}" alt="Company Logo">
            </div>
        @endif
        <div class="company-name">{{ strtoupper($company->com_name) }}</div>
        @if ($company->com_registration_no)
            <div class="company-details">{{ $company->com_registration_no }}</div>
        @endif
        <div class="company-details">{{ $company->com_address }}</div>
        <div class="company-details">{{ $company->com_postcode }} {{ $company->state_name }}</div>
        <div class="company-details">{{ $company->com_mobile }}</div>
    </div>

    <!-- Report Title -->
    <div class="report-title">PROFIT & LOSS REPORT</div>

    <!-- Details Section -->
    <div class="details-section">
        <table>
            <tr>
                <td class="header-cell" colspan="2">Details:</td>
            </tr>
            <tr>
                <td><strong>Date:</strong></td>
                <td>{{ $periodLabel }}</td>
            </tr>
            <tr>
                <td><strong>Period:</strong></td>
                <td>{{ $startDate }} to {{ $endDate }}</td>
            </tr>
            <tr>
                <td><strong>Net Profit:</strong></td>
                <td>{{ number_format($profitLoss['net_profit'], 2) }}</td>
            </tr>
            <tr>
                <td><strong>By:</strong></td>
                <td>{{ $user['userDetails']['first_name'] ?? $user['first_name'] ?? 'System' }} {{ $user['userDetails']['last_name'] ?? $user['last_name'] ?? '' }}</td>
            </tr>
            <tr>
                <td><strong>Generated:</strong></td>
                <td>{{ $generatedAt }}</td>
            </tr>
        </table>
    </div>

    <!-- Financial Statement Table -->
    <table class="financial-table">
        <thead>
            <tr>
                <th class="description-col"></th>
                <th class="amount-col"></th>
                <th class="percentage-col">%</th>
            </tr>
        </thead>
        <tbody>
            <!-- Income Section -->
            <tr class="category-row">
                <td class="description-col"><strong>Income</strong></td>
                <td class="amount-col"></td>
                <td class="percentage-col"></td>
            </tr>
            <tr>
                <td class="description-col">Sale Invoice</td>
                <td class="amount-col">{{ number_format($profitLoss['sale'], 2) }}</td>
                <td class="percentage-col">100.00</td>
            </tr>
            <tr class="total-row">
                <td class="description-col"><strong>Total</strong></td>
                <td class="amount-col"><strong>{{ number_format($profitLoss['sale'], 2) }}</strong></td>
                <td class="percentage-col"><strong>100.00</strong></td>
            </tr>

            <!-- Cost of Goods Sold Section -->
            @if(count($profitLoss['cost']['items']) > 0)
                <tr class="category-row">
                    <td class="description-col"><strong>Cost Of Goods Sold (COGS)</strong></td>
                    <td class="amount-col"></td>
                    <td class="percentage-col"></td>
                </tr>
                @foreach($profitLoss['cost']['items'] as $costItem)
                    <tr>
                        <td class="description-col">{{ $costItem['name'] }}</td>
                        <td class="amount-col">{{ number_format($costItem['amount'], 2) }}</td>
                        <td class="percentage-col">{{ $profitLoss['sale'] > 0 ? number_format(($costItem['amount'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</td>
                    </tr>
                    @foreach($costItem['subcategories'] as $subcategory)
                        <tr class="subcategory-row">
                            <td class="description-col">{{ $subcategory['name'] }}</td>
                            <td class="amount-col">{{ number_format($subcategory['amount'], 2) }}</td>
                            <td class="percentage-col">{{ $profitLoss['sale'] > 0 ? number_format(($subcategory['amount'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</td>
                        </tr>
                    @endforeach
                @endforeach
                <tr class="total-row">
                    <td class="description-col"><strong>Total</strong></td>
                    <td class="amount-col"><strong>{{ number_format($profitLoss['cost']['total'], 2) }}</strong></td>
                    <td class="percentage-col"><strong>{{ $profitLoss['sale'] > 0 ? number_format(($profitLoss['cost']['total'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</strong></td>
                </tr>
            @endif

            <!-- Gross Profit -->
            <tr class="profit-row">
                <td class="description-col"><strong>Gross Profit</strong></td>
                <td class="amount-col"><strong>{{ number_format($profitLoss['gross_profit'], 2) }}</strong></td>
                <td class="percentage-col"><strong>{{ $profitLoss['sale'] > 0 ? number_format(($profitLoss['gross_profit'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</strong></td>
            </tr>

            <!-- Expenses Section -->
            @if(count($profitLoss['expense']['items']) > 0)
                <tr class="category-row">
                    <td class="description-col"><strong>Expenses</strong></td>
                    <td class="amount-col"></td>
                    <td class="percentage-col"></td>
                </tr>
                @foreach($profitLoss['expense']['items'] as $expenseItem)
                    <tr>
                        <td class="description-col">{{ $expenseItem['name'] }}</td>
                        <td class="amount-col">{{ number_format($expenseItem['amount'], 2) }}</td>
                        <td class="percentage-col">{{ $profitLoss['sale'] > 0 ? number_format(($expenseItem['amount'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</td>
                    </tr>
                    @foreach($expenseItem['subcategories'] as $subcategory)
                        <tr class="subcategory-row">
                            <td class="description-col">{{ $subcategory['name'] }}</td>
                            <td class="amount-col">{{ number_format($subcategory['amount'], 2) }}</td>
                            <td class="percentage-col">{{ $profitLoss['sale'] > 0 ? number_format(($subcategory['amount'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</td>
                        </tr>
                    @endforeach
                @endforeach
                <tr class="total-row">
                    <td class="description-col"><strong>Total</strong></td>
                    <td class="amount-col"><strong>{{ number_format($profitLoss['expense']['total'], 2) }}</strong></td>
                    <td class="percentage-col"><strong>{{ $profitLoss['sale'] > 0 ? number_format(($profitLoss['expense']['total'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</strong></td>
                </tr>
            @endif

                         <!-- Profit & Loss -->
             <tr class="final-profit-row">
                 <td class="description-col"><strong>Profit & Loss</strong></td>
                 <td class="amount-col"><strong>{{ number_format($profitLoss['net_profit'], 2) }}</strong></td>
                 <td class="percentage-col"><strong>{{ $profitLoss['sale'] > 0 ? number_format(($profitLoss['net_profit'] / $profitLoss['sale']) * 100, 2) : '0.00' }}</strong></td>
             </tr>
        </tbody>
    </table>


</body>
</html> 