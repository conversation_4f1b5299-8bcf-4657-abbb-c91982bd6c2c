<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .company-logo {
            margin-bottom: 10px;
        }

        .company-logo img {
            max-width: 120px;
            max-height: 80px;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }

        .company-details {
            font-size: 11px;
            margin: 2px 0;
        }

        .report-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            text-decoration: underline;
        }

        .details-section {
            float: right;
            width: 200px;
            margin-bottom: 20px;
        }

        .details-section table {
            width: 100%;
            font-size: 11px;
        }

        .details-section td {
            padding: 2px 5px;
            border: 1px solid #000;
        }

        .details-section .header-cell {
            background-color: #e0e0e0;
            font-weight: bold;
            text-align: center;
        }

        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
            clear: both;
        }

        .transactions-table th {
            background-color: #e0e0e0;
            border: 1px solid #000;
            padding: 8px 4px;
            font-size: 11px;
            font-weight: bold;
            text-align: center;
        }

        .transactions-table td {
            border: 1px solid #000;
            padding: 4px;
            font-size: 10px;
            vertical-align: top;
        }

        .date-col { width: 12%; }
        .ref-col { width: 15%; }
        .payment-col { width: 15%; }
        .remark-col { width: 20%; }
        .total-col { width: 12%; text-align: right; }
        .status-col { width: 8%; text-align: center; }
        .by-col { width: 18%; }

        .no-transactions {
            text-align: center;
            font-style: italic;
            padding: 20px;
            color: #666;
        }

        @page {
            margin: 1cm;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        @if($companyLogo)
            <div class="company-logo">
                <img src="{{ $companyLogo }}" alt="Company Logo">
            </div>
        @endif
        <div class="company-name">{{ strtoupper($company->com_name) }}</div>
        @if ($company->com_registration_no)
            <div class="company-details">{{ $company->com_registration_no }}</div>
        @endif
        <div class="company-details">{{ $company->com_address }}</div>
        <div class="company-details">{{ $company->com_postcode }} {{ $company->state_name }}</div>
        <div class="company-details">{{ $company->com_mobile }}</div>
    </div>

    <!-- Report Title -->
    <div class="report-title">SALES REPORT</div>

    <!-- Details Section -->
    <div class="details-section">
        <table>
            <tr>
                <td class="header-cell" colspan="2">Details:</td>
            </tr>
            <tr>
                <td><strong>Date:</strong></td>
                <td>{{ $monthYear }}</td>
            </tr>
            <tr>
                <td><strong>Total:</strong></td>
                <td>{{ number_format($totalSales, 2) }}</td>
            </tr>
            <tr>
                <td><strong>Status:</strong></td>
                <td>Completed</td>
            </tr>
            <tr>
                <td><strong>By:</strong></td>
                <td>{{ $user['userDetails']['first_name'] ?? $user['first_name'] ?? 'System' }} {{ $user['userDetails']['last_name'] ?? $user['last_name'] ?? '' }}</td>
            </tr>
            <tr>
                <td><strong>Generated:</strong></td>
                <td>{{ $generatedAt }}</td>
            </tr>
        </table>
    </div>

    <!-- Transactions Table -->
    @if($orders->count() > 0)
        <table class="transactions-table">
            <thead>
                <tr>
                    <th class="date-col">Date</th>
                    <th class="ref-col">Ref#</th>
                    <th class="payment-col">Bank / Cash</th>
                    <th class="remark-col">Remark</th>
                    <th class="total-col">Total</th>
                    <th class="status-col">Status</th>
                    <th class="by-col">By</th>
                </tr>
            </thead>
            <tbody>
                @foreach($orders as $order)
                    <tr>
                        <td class="date-col">
                            {{ \Carbon\Carbon::parse($order->order_date)->format('d/m/y') }}<br>
                            <small>{{ \Carbon\Carbon::parse($order->order_date)->format('H:i') }}PM</small>
                        </td>
                        <td class="ref-col">{{ $order->bizapp_temp_id }}</td>
                        <td class="payment-col">{{ $order->pay_method ?? 'Cash' }}</td>
                        <td class="remark-col">{{ $order->order_note ?? '' }}</td>
                        <td class="total-col">{{ number_format($order->grandtotal_decimal, 2) }}</td>
                        <td class="status-col">Paid</td>
                        <td class="by-col">{{ optional($order->user->userDetails)->first_name ?? 'Staff' }} {{ optional($order->user->userDetails)->last_name ?? '' }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-transactions">
            <p>No transactions found for the selected period.</p>
        </div>
    @endif


</body>
</html> 