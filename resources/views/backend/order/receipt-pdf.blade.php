<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Nunito";
            -webkit-print-color-adjust: exact;
            background: #f6f6f6;
            font-size: 12pt;
        }

        .body-wrap {
            width: 100%;
            background: #f6f6f6;
        }

        .container {
            display: block !important;
            max-width: 600px !important;
            margin: 0 auto !important;
            clear: both !important;
        }

        .content {
            max-width: 600px;
            margin: 0 auto;
            display: block;
            padding: 20px;
        }

        .main {
            background: #fff;
            border: 1px solid #e9e9e9;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content-wrap {
            padding: 20px !important;
        }

        .content-block {
            /* padding: 0 0 0; */
        }

        h1 {
            font-size: 16pt !important;
            font-weight: 600;
            text-align: center;
            margin: 15px 0 !important;
            color: #2c3e50;
        }

        h4 {
            font-size: 10pt !important;
            font-weight: 500;
            margin: 6px 0 !important;
            color: #34495e;
        }

        .header-info {
            text-align: center;
            
        }

        .invoice {
            margin: 20px auto;
            width: 100% !important;
        }

        .invoice-items {
            border-top: 2px solid #34495e;
            width: 100%;
            border-collapse: collapse;
        }

        .invoice-items td {
            padding: 8px 5px;
            vertical-align: top;
        }

        .invoice-items tr:not(:last-child) {
            border-bottom: 1px solid #f0f0f0;
        }

        .number-cell {
            text-align: right;
            padding-right: 10px;
            font-family: monospace;
            white-space: nowrap;
            font-size: 10pt;
        }

        .total-section {
            margin-top: 20px;
            border-top: 0.5px solid #535557;
            padding-top: 10px;
        }

        .total-section tr td {
            padding: 6px 5px !important;
        }

        .alignright {
            text-align: right;
        }

        .currency-symbol {
            padding-right: 5px;
            color: #666;
        }

        .product-name {
            color: #2c3e50;
            font-weight: 500;
            font-size: 10pt;
        }

        .quantity {
            color: #666;
            font-size: 0.8em;
        }

        @media only screen and (max-width: 640px) {
            body {
                font-size: 10pt !important;
            }
            h1 {
                font-size: 18pt !important;
            }
            .container {
                width: 100% !important;
            }
            .content-wrap {
                padding: 15px !important;
            }
        }

        .dotted-divider {
            border-top: 2px dotted #34495e;
            margin: 20px 0;
        }
        .receipt-title {
            position: relative;
            text-align: center;
            margin: 10px 0;
        }
        .info-row {
            width: 100%;
            margin-bottom: 0px;
        }
        .info-left {
            width: 50%;
            text-align: left;
        }
        .info-right {
            width: 50%;
            text-align: right;
        }
    </style>
</head>
<body>
    <table class="body-wrap">
        <tr>
            <td></td>
            <td class="container">
                <div class="content">
                    <table class="main" width="100%">
                        <tr>
                            <td class="content-wrap">
                                <div class="header-info">
                                    <!-- Company Logo -->
                                    @if(isset($companyLogo) && $companyLogo)
                                        <div style="text-align: center; margin-bottom: 20px;">
                                            <img src="{{ $companyLogo }}" alt="Company Logo" style="max-width: 120px; max-height: 80px; object-fit: contain;">
                                        </div>
                                    @endif
                                    
                                    <div style="margin-bottom: 30px;">
                                        <h1 style="text-transform: uppercase; margin: 0 0 2px 0;">{{ $company->com_name }}</h1>
                                        @if ($company->com_registration_no)
                                            <h4 style="margin: 0; line-height: 1.2;">( {{ $company->com_registration_no }} )</h4>                                            
                                        @endif
                                        @if($company->com_sst_number)
                                            <h4 style="margin: 0; line-height: 1.2;">SST NO : {{ $company->com_sst_number }}</h4>
                                        @endif
                                    </div>
                                    <h4>{{ $company->com_address }}</h4>
                                    <h4 style="text-transform: uppercase;">{{ $company->com_postcode . ' ' . $company->state_name }}</h4>
                                    <h4>TEL : {{ $company->com_mobile }}</h4>
                                    <h4>EMAIL : {{ $company->com_email }}</h4>
                                    <h4>SALES PERSON : {{ $order->user?->userDetails->first_name }}</h4>
                                </div>

                                <div class="dotted-divider"></div>
                                <h1 class="receipt-title">RECEIPT</h1>
                                <div class="dotted-divider"></div>

                                <div class="content-block">
                                    <table class="info-row">
                                        <tr>
                                            <td class="info-left">
                                                <h4>DATE : </h4>
                                            </td>
                                            <td class="info-right">
                                                <h4>{{ $order->created_at->format('Y-m-d H:i') }}</h4>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="info-left">
                                                <h4>INVOICE NO : </h4>
                                            </td>
                                            <td class="info-right">
                                                <h4>{{ $order->bizapp_temp_id }}</h4>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <table class="invoice">
                                    <h4>Total Items: {{ count($order->orderDetails) }}</h4>
                                    <tr>
                                        <td>
                                            <table class="invoice-items">
                                                @foreach ($order->orderDetails as $item)
                                                <tr>
                                                    <td class="product-name">
                                                        {{ $item->productName }}
                                                        <div class="quantity">Qty: {{ $item->product_qty }}</div>
                                                    </td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell">{{ number_format($item->product_price, 2) }}</td>
                                                </tr>
                                                @endforeach
                                                <br>
                                                <tr class="total-section">
                                                    <td class="alignright">Subtotal</td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell">{{ number_format($order->subtotal, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="alignright">Discount</td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell">{{ number_format($order->discounts, 2) }}</td>
                                                </tr>
                                                @if ($order->sst_value)
                                                    <tr>
                                                        <td class="alignright"><strong>SST {{ $order->sst_value }}%</strong></td>
                                                        <td class="currency-symbol">{{ $currency }}</td>
                                                        <td class="number-cell"><strong>{{ number_format($order->sst_amount, 2) }}</strong></td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td class="alignright"><strong>Grand Total</strong></td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell"><strong>{{ number_format($order->grandtotal, 2) }}</strong></td>
                                                </tr>
                                                <tr>
                                                    <td class="alignright">Paid Amount</td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell">{{ number_format($order->payment_received, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="alignright">Balance</td>
                                                    <td class="currency-symbol">{{ $currency }}</td>
                                                    <td class="number-cell">{{ number_format($order->payment_balance, 2) }}</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
            <td></td>
        </tr>
    </table>
</body>
</html>