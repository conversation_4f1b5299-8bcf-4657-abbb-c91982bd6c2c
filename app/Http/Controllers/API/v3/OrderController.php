<?php

namespace App\Http\Controllers\API\v3;

use Exception;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Shift;
use App\Models\Company;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Employee;
use App\Models\LogStock;
use App\Models\OrderDetail;
use Illuminate\Http\Request;
use App\Models\CompanyBranch;
use App\Models\Payment\Gkash;
use App\Models\CommerceasiaPay;
use App\Jobs\SyncOrdersByPIDJob;
use App\Jobs\RetryStockUpdateJob;
use App\Jobs\GetLocationDetailJob;
use App\Jobs\SendOrderToBizappJob;
use App\Jobs\SyncProductBizappJob;
use Illuminate\Support\Facades\DB;
use App\Jobs\RegisterBizappUserJob;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Jobs\CreateProductFromBizapp;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use App\Jobs\AddMissingProductFromBizappJob;
use App\Jobs\SyncSingleProductFromBizappJob;
use App\Models\Receipt;
use App\Models\ReceiptSnapshot;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class OrderController extends Controller
{

    public function index(Request $request){
        $start_date = (empty($request->from_date)) ? date('Y-m-d') : $request->from_date;
        $end_date = (empty($request->to_date)) ? date('Y-m-d') : $request->to_date;
        $user = auth()->user();
        if(!$user){
            return $this->sendResponse(false, "Please log in first");
        }

        $query = Order::query();
        $query->with('orderDetails');
        $query->where('user_id', $user->id);
        $query->where('order_date', '>=', $start_date);
        $query->where('order_date', '<=', $end_date);
        $query->when($request->search, function($q) use($request){
            $q->where('order_number', 'like', '%' . $request->search .'%');
        });
        $query->orderBy('order_number', 'desc');

        $list = $query->paginate($request->show ?? 10);
        $array = [];

        foreach($list as $item){
            $items = $item->orderDetails;
            $array[] = [
                'id' => $item->id,
                'created_at' => $item->created_at->format('Y-m-d H:i:s'),
                'order_number' => $item->order_number,
                'customer_name' => $item->customer_name,
                'total_item' => count($items),
                'total_qty' => $items->sum('product_qty'),
                'note' => $item->order_label,
                'status' => $item->status,
                'payment' => [
                    'total' => $item->grandtotal,
                    'discount' => $item->discounts,
                    'rounding' => $item->roundingAdjustment,
                    'pay_received' => $item->payment_received,
                    'pay_balance' => $item->payment_balance
                ],
                'items' => $items->map(function($item){
                    return [
                        'id' => $item->id,
                        'name' => $item->productName,
                        'sku' => $item->product_SKU,
                        'price' => $item->product_price,
                        'quantity' => $item->product_qty,
                        'discount' => $item->product_discount_amount
                    ];
                })
            ];
        }

        $data = [
            'list' => $array,
            'pagination' => [
                'total' => $list->total(),
                'per_page' => $list->perPage(),
                'current_page' => $list->currentPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem()
            ]
        ];

        return $this->sendResponse(true, "Success", $data);
    }

    public function getOrderHistory()
    {
        $user = auth()->user();

        $userData = getUser();

        if($userData['userRole'] == 'HQ'){
            $orders = Order::where('company_id', $userData['company']['id'])
            ->whereNull('deleted_at')
            ->with('orderDetails')
            ->orderBy('order_date', 'DESC')
            ->paginate(50);
        } else {
            $orders = Order::where('user_id', $user->id)
            ->whereNull('deleted_at')
            ->with('orderDetails')
            ->orderBy('order_date', 'DESC')
            ->paginate(50);
        }

        if(!$orders){
            return response()->json([
                'status' => '0',
                'message' => 'No orders made yet.',
            ]);
        } else {

            return response()->json([
                'status' => '1',
                'message' => 'Success',
                'data' => $orders,
            ]);
        }
    }

    public function getOrderHistoryAll(Request $request)
    {
        try{
            $user = auth()->user();
            $company = Company::select('id', 'user_id')->where('user_id', $user['id'])->first();
            $company_id = null;

            if ($company) {
                $company_id = $company->id;
            } else {
                $employee = Employee::select('user_id', 'company_id')->where('user_id', $user['id'])->first();
                if ($employee) {
                    $company_id = $employee->company_id;
                }
            }

            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();

            if($request->has('start_date') && $request->has('end_date')){
                $startDate = Carbon::parse($request->start_date)->format('Y-m-d H:i:s');
                $endDate = Carbon::parse($request->end_date)->format('Y-m-d' . ' 23:59:59');
            }
            $orders = Order::where(function ($query) use ($user, $company_id) {
                $query->where('user_id', $user->id)
                      ->orWhere('company_id', $company_id);
            })
            ->with('orderDetails')
            ->orderBy('order_date', 'DESC')
            ->whereBetween('order_date', [$startDate, $endDate]);

            if ($request->has('deleted_only') && $request->deleted_only == true) {
                $orders->onlyTrashed();
            }

            if ($request->has('search') && $request->search != null) {
                $search = $request->search;
                $orders->where(function ($query) use ($search) {
                    $query->where('customer_name', 'like', "%{$search}%")
                          ->orWhere('bizapp_temp_id', 'like', "%{$search}%")
                          ->orWhere('bizapp_order_id', 'like', "%{$search}%")
                          ->orWhere('order_notes', 'like', "%{$search}%")
                          ->orWhere('cartID', 'like', "%{$search}%")
                          ->orWhere('cap_id', 'like', "%{$search}%");
                });
            }

            if(!$orders){
                return response()->json([
                    'status' => '0',
                    'message' => 'No orders made yet.',
                ]);
            } else {

                return response()->json([
                    'status' => '1',
                    'message' => 'Success',
                    'data' => $orders->paginate(50),
                ]);
            }
        } catch (\Exception $e){
            Log::error('Retrieve order history all failed : ' . $e->getMessage());
            return response()->json([
                'status' => '0',
                'message' => 'Retrieving failed : ' . $e->getMessage()
            ]);
        }
    }

    public function getOrderHistoryDetail($orderId)
    {
        $user = auth()->user();

        $details = OrderDetail::where('order_id',$orderId)->get();

        if(!$details){
            return response()->json([
                'status' => '0',
                'message' => 'Order detail not found',
            ]);
        } else {
            return response()->json([
                'status' => '1',
                'message' => 'Success',
                'data' => $details
            ]);
        }
    }

    public function store(Request $request){
        $user = getUser();
        if(!$user){
            return $this->sendResponse(false, "Please log in first");
        }

        $validator = Validator::make($request->all(), [
            'bizapp_temp_id' => 'required|string',
            'payMethod' => 'required|string',
            'subtotal' => 'required|decimal:0,2',
            'roundingAdjustment' => 'required|decimal:0,2',
            'pay_received' => 'required|decimal:0,2',
            'pay_balance' => 'required|decimal:0,2',
            'grandtotal' => 'required|decimal:0,2',
            'items' => 'required|array'
        ]);

        if($validator->fails()){
            return $this->sendResponse(false, $validator->errors()->first());
        }

        if(Order::where(['bizapp_temp_id' => $request->bizapp_temp_id, 'user_id' => $user['id']])->exists()){
            return $this->sendResponse(false, "Temp ID Already exists");
        }
        try {
            DB::beginTransaction();

            $order = Order::create([
                'bizapp_temp_id' => $request->bizapp_temp_id,
                'user_id' => $user['id'],
                // 'customer_id' => $customer->id ?? null,
                // 'customer_name' => $customer->first_name . " " . $customer->last_name ?? "GUEST",
                // 'shift_id' => $shift->id,
                'cartID' => $request->cartID ?? null,
                'order_number' => Order::generateOrderNumber(),
                'pay_method' => $request->payMethod,
                'subtotal' => $request->subtotal,
                'roundingAdjustment' => $request->roundingAdjustment,
                'payment_received' => $request->pay_received,
                'payment_balance' => $request->pay_balance,
                'discount' => $request->discount,
                'grandtotal' => $request->grandtotal,
                'order_label' => $request->orderLabel ?? null,
                'order_image' => $request->orderImage ?? null,
                'order_date' => Carbon::today(),
                'status' => "SUCCESS"
            ]);

            $items = collect($request->items);
            $skus = $items->pluck('sku')->toArray();
            $details = [];

            $products = Product::whereIn('product_SKU', $skus)->where('parent_company', $user['company']['id'])->get();
            foreach($products as $product){
                try {

                $item = $request->items[array_search($product->product_SKU, array_column($request->items, 'sku'))];
                $id = $item['id'];
                $name = $item['name'];
                $price = $item['price'];
                $quantity = $item['quantity'];
                $sku = $item['sku'];
                $discount = $item['discount'] ?? '0.00';

                if($product->product_stock_status == "N"){
                    // TODO: find a way to handle no stock issue better
                    if($product->product_stock < $quantity){
                        DB::rollBack();
                        return $this->sendResponse(false, "Stock [ {$product->product_name} ] is not enough");
                    }
                    $product->decrement('product_stock', $quantity);
                    LogStock::create([
                        'company_id' => $user['company']['id'],
                        'user_id' => $user['id'],
                        'product_id' => $product->id,
                        'name' => $user['user']['first_name'] . $user['user']['last_name'],
                        'product_name' => $name,
                        'product_sku' => $sku,
                        'quantity' => $quantity,
                        'remain' => $product->product_stock,
                        'note' => "Order POS #{$order->order_number}"
                    ]);
                }

                    $orderDetail = OrderDetail::create([
                        'order_id' => $order->id,
                        'productName' => $name,
                        'product_SKU' => $sku,
                        'product_price' => $price,
                        'product_qty' => $quantity,
                        'product_discount_amount' => $discount != null ? number_format((float)$discount, 2, '.', '') : 0.00,
                        'bizapp_product_id' => $id,
                    ]);

                    $details[] = [
                        'id' => $id,
                        'name' => $name,
                        'price' => $price,
                        'sku' => $sku,
                        'quantity' => $quantity,
                        'discount' => $discount
                    ];
                } catch (\Exception $e) {
                    Log::error("Failed to create Order & Detail v3: " . $e->getMessage());
                }
            }

            $data = [
                'saveOrder' => $order,
                'orderDetail' => $details,
                'products' => $products
            ];

            DB::commit();
            return $this->sendResponse(true, "Success", $data);
        } catch (Exception $e){
            DB::rollBack();
            return $this->sendResponse(false, $e->getMessage());
            // return $this->sendResponse(false, "System error, please try again");
        }
    }

    public function storeFromLocalQueue(Request $request){
        Log::info("Save order on v3 start: ");
        $validator = Validator::make($request->all(), [
            // 'id' => 'required|exists:orders,id',
            'details' => 'required|array'
        ]);

        if ($validator->fails()) {
            Log::error("save order v3 failed : Validation error - " . json_encode($validator->errors()) . ' request : ' . json_encode($request->all()));
            return response()->json([
                'status' => '0',
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = getUser();
        if(!$user){
            // return $this->sendResponse(false, "Please log in first");
            return response()->json([
                'result' => false,
                'status' => '0',
                'message' => 'Please log in first',
                'code' => 'SO1'
            ]);
        }

        Log::info("Creating order data from user: " . json_encode($request->all()));
        $company = Company::select('id', 'user_id')->where('user_id', $user['id'])->first();
        $company_id = null;

        if ($company) {
            $company_id = $company->id;
        } else {
            $employee = Employee::select('user_id', 'company_id')->where('user_id', $user['id'])->first();
            //TODO: some users dont have employee record even when they are not HQ, do a create function in remote login
            if ($employee) {
                $company_id = $employee->company_id;
            }
        }
        $userData = auth()->user();
        // Check for existing order with the same order_date and pay_provider
        $existingOrder = Order::where('order_date', $request->order_date)
        ->where('payment_provider', $request->pay_provider)
        ->where('user_id', $user['id']) // Optional: filter by user ID if needed
        ->first();

        if ($existingOrder) {
        Log::info("Creating order data from user failed: exact order_date and user_id already exists" . json_encode($request->all()));
        // return $this->sendResponse(false, "An order with the same date , user_id and payment provider already exists.");
        return response()->json([
            'result' => false,
            'status' => '0',
            'message' => 'An order with the exact datetime and payment provider already exists.',
            'code' => 'SO2'
        ]);
        }

        // Check for existing order with the same order_date and pay_provider
        $existingOrder2 = Order::where('bizapp_temp_id', $request->temp_id)
        ->where('payment_provider', $request->pay_provider)
        ->where('user_id', $user['id']) // Optional: filter by user ID if needed
        ->first();

        if ($existingOrder2) {
        Log::info("Creating order data from user failed: exact temp_id and user_id already exists" . json_encode($request->all()));
        // return $this->sendResponse(false, "An order with the same temp_id and payment provider already exists.");
        return response()->json([
            'result' => false,
            'status' => '0',
            'message' => 'An order with the same temp_id , user_id and payment provider already exists.',
            'code' => 'SO3'
        ]);
        }

        try {
            
            DB::beginTransaction();

            // --- Receipt Snapshot Logic Start ---
            $receiptSnapshotId = null;

            $branchId = null;
            $employee = Employee::where('user_id', $user['id'])->first();
            if ($employee && $employee->branch_id) {
                $branchId = $employee->branch_id;
            }

            $currentReceipt = null;
            if ($branchId) {
                $currentReceipt = Receipt::where('branch_id', $branchId)->first();
            }
            if (!$currentReceipt && $company_id) {
                $currentReceipt = Receipt::where('company_id', $company_id)->whereNull('branch_id')->first();
            }

            if ($currentReceipt) {
                try {
                    $snapshot = ReceiptSnapshot::create([
                        'receipt_id' => $currentReceipt->id,
                        'user_id' => $currentReceipt->user_id,
                        'company_id' => $currentReceipt->company_id,
                        'branch_id' => $currentReceipt->branch_id,
                        'name' => $currentReceipt->name,
                        'address' => $currentReceipt->address,
                        'postcode' => $currentReceipt->postcode,
                        'city' => $currentReceipt->city,
                        'state' => $currentReceipt->state,
                        'country' => $currentReceipt->country,
                        'email' => $currentReceipt->email,
                        'phone' => $currentReceipt->phone,
                        'ssm' => $currentReceipt->ssm,
                        'sst' => $currentReceipt->sst,
                        'tnc' => $currentReceipt->tnc,
                        'logo' => $currentReceipt->logo,
                        'qr' => $currentReceipt->qr,
                    ]);
                    $receiptSnapshotId = $snapshot->id;
                } catch (\Exception $e) {
                    Log::error("Failed to create receipt snapshot for user {$user['id']} (company {$company_id}, branch {$branchId}): " . $e->getMessage());
                }
            } else {
                Log::warning("No receipt found for user {$user['id']} (company {$company_id}, branch {$branchId}) during order creation. Snapshot not created.");
            }
            // --- Receipt Snapshot Logic End ---

            // TODO: store new customer if new customer is added

            $order = Order::create([
                'bizapp_temp_id' => $request->temp_id,
                'user_id' => $user['id'],
                'company_id' => $company_id ?? null, // if null, user's HQ is not in the DB
                'customer_id' => $request->customerid ?? null,
                'customer_name' => $request->customer_name ?? "POS-" .$request->temp_id,
                // 'shift_id' => $shift->id,
                'cartID' => $request->cartID ?? null,
                'cap_id' => $request->cap_id ?? null,
                'order_number' => Order::generateOrderNumber(),
                'pay_method' => $request->pay_method,
                'subtotal' => $request->subtotal,
                'roundingAdjustment' => $request->roundingAdjustment,
                'payment_received' => $request->payment_received,
                'payment_balance' => $request->payment_balance,
                'discounts' => $request->discounts,
                'grandtotal' => $request->grandtotal,
                'subtotal_decimal' => number_format((float)$request->subtotal, 2, '.', ''),
                'roundingAdjustment_decimal' => number_format((float)$request->roundingAdjustment, 2, '.', ''),
                'payment_received_decimal' => number_format((float)$request->payment_received, 2, '.', ''),
                'payment_balance_decimal' => number_format((float)$request->payment_balance, 2, '.', ''),
                'discounts_decimal' => number_format((float)$request->discounts, 2, '.', ''),
                'grandtotal_decimal' => number_format((float)$request->grandtotal, 2, '.', ''),
                'order_label' => $request->order_label ?? null,
                'order_image' => $request->order_image ?? null,
                'order_date' => $request->order_date,
                'receipt_snapshot_id' => $receiptSnapshotId, // Link order to the snapshot
                'order_notes' => $request->order_notes ?? null,
                'order_latitude' => $request->latitude ?? null,
                'order_longitude' => $request->longitude ?? null,
                'coupon_for' => $request->coupon_for ?? null,
                'coupon_code' => $request->coupon_code ?? null,
                'coupon_value' => number_format((float)$request->coupon_value, 2, '.', ''),
                'payment_provider' => $request->pay_provider ?? null,
                'sst_value' => $request->sst_value ?? null,
                'sst_amount' => number_format((float)$request->sst_amount, 2, '.', '') ?? 0.00,
                'status' => "SUCCESS"
            ]);
            
            $customerData = null;
            if ($request->customerid) {
                // Fetch the existing customer by customerid
                $customerData = Customer::where('id', $request->customerid)->first();
            } 
            // insert customer info if available and if phone number already exists in DB
            else if (!empty(trim($request->customer_name)) && !Customer::phoneAndCompanyExist($request->customer_phoneno, $company_id)) {
                $customerData = Customer::create([
                    'company_id' => $company_id ?? null,
                    'first_name' => $request->customer_name,
                    'email' => $request->customer_email ?? null,
                    'phone' => $request->customer_phoneno ?? null,
                    'address' => $request->customer_address ?? null,
                ]);
            } else {
                // TODO: handle if customer phone number already exists in DB | To skip or save?
                Log::info('saving customer data is NULL');
            }

            $items = collect($request->details);
            Log::info('order details : ' . $items);
            // $skus = $items->pluck('product_sku')->toArray();
            $details = [];

            // $products = Product::whereIn('product_SKU', $skus)->where('parent_company', $user['company']['id'])->get();
            foreach($items as $item){
                try {
                // $item = $request->details[array_search($product->product_sku, array_column($request->details, 'product_sku'))];
                $productIdBizapp = $item['bizapp_product_id'];
                $productId = $item['product_id'];
                $name = $item['product_name'];
                $price = $item['product_price'];
                $quantity = $item['product_qty'];
                $sku = $item['product_sku'];
                $discount = $item['product_discount'] ?? '0.00';
                $product = Product::where('id', $productId)->first();

                    $orderDetail = OrderDetail::create([
                        'order_id' => $order->id,
                        'productName' => $name,
                        'product_id' => $productId,
                        'product_SKU' => $sku ?? "",
                        'product_price' => $price,
                        'product_price_decimal' => number_format((float)$price, 2, '.', ''),
                        'product_qty' => $quantity,
                        'product_discount_amount' => $discount,
                        'product_discount_amount_decimal' => number_format((float)$discount, 2, '.', ''),
                        'bizapp_product_id' => $productIdBizapp,
                    ]);

                    $details[] = [
                        'id' => $productId,
                        'bizapp_product_id' => $product->product_id_bizapp,
                        'name' => $name,
                        'price' => $price,
                        'sku' => $sku ?? "",
                        'quantity' => $quantity,
                        'discount' => $discount
                    ];
                } catch (\Exception $e) {
                    Log::error("Failed to create Order & Detail v3: " . $e->getMessage());
                    logFailedOrder('Backoffice',$request->all(), $e->getMessage());
                }

                // record stock movement if not ready stock
                if($product && $product->product_stock_status == 'N'){
                    $product_stock = (int) $product->product_stock;
                    // Calculate remain, ensuring it does not go negative
                    $remain = $quantity > $product_stock ? 0 : $product_stock - $quantity;
                    LogStock::create([
                        'company_id' => $company_id ?? '',
                        'user_id' => $user['id'],
                        'product_id' => $product->id,
                        'name' => $user['username'],
                        'product_name' => $name,
                        'product_sku' => $sku ?? "",
                        'quantity' => $quantity,
                        'remain' => $remain,
                        'note' => 'Order submission',
                    ]);

                    $product->product_stock = $remain;
                    $product->save();
                }
            }

             // saving orders with commerceasia_pay in their table
             if($request->pay_provider === 'CAP')
             {
                CommerceasiaPay::create([
                    'company_id' => $company_id,
                    'user_id' => $user['id'],
                    'order_id' => $order->id,
                    'transaction_id' => $request->cap_id,
                    'cap_timestamp' => $request->cap_timestamp,
                    'ref_code' => $request->ref_code ?? null,
                    'status' => $request->cap_status,
                ]);
             }

            $data = [
                'saveOrder' => $order,
                'orderDetail' => $details,
                // 'products' => $products
            ];

            DB::commit();
            Log::info('Dispatching SendOrderToBizappJob', [
                'order_id' => $order->id,
                'user_domain' => $userData->domain,
                'user_pid' => $userData->pid
            ]);

            // get location details
            // if($order->order_latitude != null)
            // {
            //     GetLocationDetailJob::dispatch($order)->onQueue('getLocationDetail');
            // }
            // making sure the $details is not null
            if($details[0]['id'] != null)
            {
                Log::info("Processing order details: " . json_encode($details));
                if($userData->isBizappUser == 'Y'){
                    // Prepare customer data or use defaults if $customerData is null
                    $customerDataArray = $customerData ? [
                        'hpno' => $customerData->phone,
                        'email' => $customerData->email,
                        'address' => $customerData->address,
                    ] : [
                        'hpno' => null,
                        'email' => null,
                        'address' => null,
                    ];
                    
                    SendOrderToBizappJob::dispatch($order, $details, $userData->domain, $userData->pid,$customerDataArray)->onQueue('sendOrderToBizapp');
                }
                return $this->sendResponseV3(true, '1', "Success", $data, 200);
            } else {
                Log::info('Saving failed',[$order,$details]);
                return $this->sendResponseV3(false, '0', "Failed", $details, 500);
                logFailedOrder('Backoffice',$request->all(), 'Details : ' . $details);
            }


        } catch (Exception $e){
            Log::error('Saving order v3 failed ' . $e->getMessage());
            DB::rollBack();
            logFailedOrder('Backoffice',$request->all(), $e->getMessage());
            return $this->sendResponse(false, $e->getMessage());
        }
    }

    public function cancel(Request $request){
        $user = getUser();
        if(!$user){
            return $this->sendResponse(false, "Please login first");
        }

        $order = Order::with('orderDetails','orderDetails.product')->where([
            'id' => $request->id,
            'user_id' => $user['id']
        ])->first();
        if(!$order){
            return $this->sendResponse(false, "Order not found");
        } else if($order->status == "CANCEL"){
            return $this->sendResponse(false, "Order already canceled");
        }

        foreach($order->orderDetails as $item){
            $product = $item->product;
            if($product){
                $name = $product->product_name;
                $sku = $product->product_SKU;
                $quantity = $item->product_qty;

                if($product->product_stock_status == "N"){
                    $product->increment('product_stock', $quantity);
                    LogStock::create([
                        'company_id' => $user['company']['id'],
                        'user_id' => $user['id'],
                        'product_id' => $product->id,
                        'name' => $user['username'],
                        'product_name' => $name,
                        'product_sku' => $sku,
                        'quantity' => $quantity,
                        'remain' => $product->product_stock,
                        'note' => "Cancel Order POS #{$order->order_number}"
                    ]);
                }
            }
        }

        $order->update([
            'status' => 'CANCEL'
        ]);
        $order->delete();

        $title = "Cancel Order";
        $description = "Cancel order pos , with order id [{$order->id}]";
        addStaffActivity($title, $description);

        return $this->sendResponse(true, "success", $order);
    }

    public function editNote(Request $request)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:orders,id',
            'new_note' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            Log::info("Edit note order v3 failed : Validation error - " . json_encode($validator->errors()));
            return response()->json([
                'status' => '0',
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = auth()->user();
        if(!$user){
            Log::info("Edit note v3 failed : User not found - {$request->id}");
            return response()->json([
                'status' => '0',
                'message' => 'User not found'
            ]);
        }
        try{
        $order = Order::findOrFail($request->id);
        Log::info("Edit note v3 : Order data : {$order}");
        Log::info("Edit note v3 : Order id - {$request->id} | New Note : {$request->new_note}");

            $order->order_notes = $request->new_note;
            $saved = $order->save();

            if (!$saved) {
                Log::error("Edit note v3 : Failed to save order note. Order ID: {$request->id}");
                return response()->json([
                    'status' => '0',
                    'message' => 'Failed to update order note'
                ], 500);
            }
            DB::enableQueryLog();
            // Log all queries executed up to this point
            Log::info("Edit note v3 : SQL Queries executed: " . json_encode(DB::getQueryLog()));

            Log::info("Edit note v3 : Order data after update: " . json_encode($order->fresh()->toArray()));
            return response()->json([
                'status' => '1',
                'message' => 'Order note updated successfully'
            ], 200);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::info("Edit note v3 failed : Order not found - {$request->id}");
            return response()->json([
                'status' => '0',
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error("Edit note v3 failed : {$e->getMessage()}");
            return response()->json([
                'status' => '0',
                'message' => 'Failed to update order note'
            ], 500);
        }

    }

    public function cancelOrderV3(Request $request)
    {
         // Validate input
            $validator = Validator::make($request->all(), [
                'id' => 'required|exists:orders,id',
                'note' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                Log::info("Cancel order v3 failed : Validation error - " . json_encode($validator->errors()));
                return response()->json([
                    'status' => '0',
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

        $user = auth()->user();
        if(!$user){
            Log::info("Cancel order v3 failed : User not found - {$request->id}");
            return response()->json([
                'status' => '0',
                'message' => 'User not found'
            ],401);
        }

        try {
                $order = Order::findOrFail($request->id);
                // Check if the order belongs to the authenticated user
                if ($order->user_id !== $user->id) {
                    Log::info("Cancel order v3 failed : Unauthorized access - User ID: {$user->id}, Order ID: {$request->id}");
                    return response()->json([
                        'status' => '0',
                        'message' => 'Unauthorized access to this order'
                    ], 403);
                }

                Log::info("Cancel order v3 : Order data before cancellation: " . json_encode($order->toArray()));
                // Retrieve the order details associated with the order
                $orderDetails = DB::table('order_details')
                ->where('order_id', $order->id)
                ->get();

                    foreach ($orderDetails as $detail) {
                    // Find the associated product
                    $product = DB::table('products')->where('id', $detail->product_id)->first();
                   

                    if ($product) {
                        // Increment the product's stock count by the quantity in the order detail

                        if($product->product_stock != -100){
                            DB::table('products')
                            ->where('id', $product->id)
                            ->increment('product_stock', $detail->product_qty);
                        }
                        

                        LogStock::create([
                            'company_id' => $order->company_id ?? '',
                            'user_id' => $user->id,
                            'product_id' => $product->id,
                            'name' => $user->username ?? 'update from bizapp web',
                            'product_name' => $product->product_name,
                            'product_sku' => $product->product_SKU ?? "",
                            'quantity' => (int) $detail->product_qty,
                            'remain' => $product->product_stock,
                            'note' => $request->note ?? 'Cancel order',
                        ]);
                        

                        Log::info("Cancel order v3 : Order successfully cancelled - ID: {$request->id}");
        
                        if(isBizappUserCheck()){
                            $this->cancelOrderToBizapp($order->bizapp_order_id,$product->product_id_bizapp);
                        }

                        Log::info("Restored stock for product ID: {$product->id}, Quantity: {$detail->product_qty}");
                    } else {
                        Log::error("Product not found for order detail ID: {$detail->id}, Product ID: {$detail->product_id}");
                    }
                }
                $newNote = 'CANCEL : ' . $request->note;
                $order->order_notes = empty($order->order_notes) ? $newNote : $newNote . ' || ' . $order->order_notes;
                $order->delete();
               

                return response()->json([
                    'status' => '1',
                    'message' => 'Order removed successfully'
                ], 200);
            } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
                Log::info("Cancel order v3 failed : Order not found - {$request->id}");
                return response()->json([
                    'status' => '0',
                    'message' => 'Order not found'
                ], 404);
            } catch (\Exception $e) {
                Log::error("Cancel order v3 failed : {$e->getMessage()}");
                return response()->json([
                    'status' => '0',
                    'message' => 'Failed to update order note'
                ], 500);
            }

    }

    public function cancelOrderFromBizapp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,bizapp_order_id',
            'domain' => 'required|string',
            'pid' => 'required|string',
            'action' => 'required|string'
        ]);

        if ($validator->fails()) {
            Log::error("Cancel order from bizapp failed : Validation error - " . json_encode($validator->errors()) . " PID : " . $request->pid);
            return response()->json([
                'status' => '0',
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('pid',$request->pid)->where('domain',$request->domain)->first();
        if(!$user){
            Log::error("Cancel order from bizapp failed : user not found - " . $request->pid);
            return response()->json([
                'status' => '0',
                'message' => 'user not found in bizappos'
            ]);
        }

        try {
            $order = Order::withTrashed()->where('bizapp_order_id',$request->order_id)->where('user_id',$user->id)->first();

            // Add null check before logging
            if (!$order) {
                Log::error("Cancel order from bizapp failed: Order not found", [
                    'request' => $request->all(),
                    'user' => $user->toArray()
                ]);
                return response()->json([
                    'status' => '0',
                    'message' => 'Order id not found in Bizappos'
                ], 404);
            }

            // Safe logging with null check
            Log::info("Cancel order from bizapp : Order data before cancellation", [
                'order_id' => $order->id,
                'bizapp_order_id' => $order->bizapp_order_id,
                'status' => $order->status,
                'request' => $request->all()
            ]);

            if($request->action === "0"){ // to delete / hapus
                $order->update([
                    'status' => 'DELETED',
                ]);
                $order->delete();

                return response()->json([
                    'status' => '1',
                    'message' => 'Order id '. $request->order_id .' HAPUS'
                ], 200);
            } else if ($request->action === "1"){ // to batal , tak delete
                $order->update([
                    'status' => 'CANCEL',
                ]);

                return response()->json([
                    'status' => '1',
                    'message' => 'Order id '. $request->order_id .' BATAL'
                ], 200);
            
            // } else if($request->action === "2"){
            //     $order->update([
            //         'status' => 'RECOVERED'
            //     ]);
            //     $order->restore();

            //     return response()->json([
            //         'status' => '1',
            //         'message' => 'Order id '. $request->order_id .' RECOVER'
            //     ], 200);

            } else {
                
                return response()->json([
                    'status' => '0',
                    'message' => 'Check the action parameter value'
                ]);
            }



        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::info("Cancel order from bizapp failed : Order not found - {$request->id}");
            return response()->json([
                'status' => '0',
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error("Cancel order from bizapp failed : {$e->getMessage()}");
            return response()->json([
                'status' => '0',
                'message' => 'Failed to update order status'
            ], 500);
        }
    }

    public function stockUpdateFromBizapp(Request $request)
    {
        if($request->action != 'updatestock' && $request->action != 'delete' && $request->action != 'create' && $request->action != 'updateproduct'){
            Log::error("stockUpdateFromBizapp v31: action data is empty " . json_encode($request->all()));
            return response()->json([
                'status' => '0',
                'message' => 'action data is empty, please specify (updatestock/delete/create)'
            ]);
        }

        // check if the billstok is in the correct integer format
       if ($request->action == 'updatestock' && !preg_match('/^-?\d+$/', $request->billstok)) {
        Log::error("stockUpdateFromBizapp v31: billstok is not in correct integer format");
            return response()->json([
                'status' => '0',
                'message' => 'billstok must be a whole number in integer format'
            ]);
        }

        // Retrive User info from Cache. If not exist callback retrive from DB and add to Cache. 
        $user = Cache::remember("user:{$request->pid}:{$request->domain}", now()->addHours(1), function () use ($request) {
            return User::select('id', 'pid', 'domain')->where('pid', $request->pid)->where('domain', $request->domain)->first();
        });

        if (!$user) {
            // Log::error("stockUpdateFromBizapp : User not found: pid={$request->pid}, domain={$request->domain} in stockUpdateFromBizapp function");
            // Log::error("stockUpdateFromBizapp v31: user not found " . $request->pid);
            return response()->json([
                'status' => '0',
                'message' => 'User not found in bizappos'
            ]);
        }
        
        // Retrive Company info from Cache. If not exist callback retrive from DB and add to Cache. 
        $company = Cache::remember("company:user:{$user->id}", now()->addHours(1), function () use ($user) {
            return Company::select('id')->where('user_id', $user->id)->first();
        });
        
        if (!$company) {
        // Cache employee and branch data
        $employeeAndBranch = Cache::remember("employee_branch:user:{$user->id}", now()->addHours(1), function () use ($user) {
        $employee = Employee::select('id')->where('user_id', $user->id)->first();
        if ($employee) {
            $branch = CompanyBranch::select('branch_of')->where('person_in_charge', $employee->id)->first();
            if ($branch) {
                return ['branch_of' => $branch->branch_of];
                }
            }
        });

        if (!$employeeAndBranch) {
            Log::error("stockUpdateFromBizapp v31: No company or branch found for user: {$user->id}");
            return response()->json([
                'status' => '0',
                'message' => 'No associated company or branch found'
            ]);
        }

            $parentCompanyId = $employeeAndBranch['branch_of'];
        } else {
            $parentCompanyId = $company->id;
        }


        Log::info("stockUpdateFromBizapp v3 START : " . json_encode($request->all()));

       
        
        // Extract 'action' parameter
        $action = $request->action;
        // Define stock status
        $stockStatus = $request->billstok == -100 ? 'Y' : 'N';
        // Log::info('v3-stockUpdateFromBizapp [/action : ' . $action. ']');
        
        
        // More informative Log format
        Log::info('v3-stockUpdateFromBizapp', [
            'action' => $action,
            'stockStatus' => $stockStatus,
            'user' => [
                //'id' => $user->id,
                'pid' => $user->pid,
                'domain' => $user->domain,
            ],
            'product' => [
                //'id' => $findProduct->id,
                'product_id_bizapp' => $request->productid,
                'product_stock' => $request->billstok,
            ],
            'updated_quantity' => $request->billstok,
            'status' => 'initiated'
        ]);
        
        if($request->action == 'updatestock'){
            // Update or create product
            try{
                ////////////////////////////
                // Retrieve products from DB
                $findProduct = Product::where('product_id_bizapp', $request->productid)
                ->orWhere('product_id_bizapp',$request->productid_old)
                ->orWhere('product_SKU',$request->sku)
                ->orWhere('product_SKU',$request->sku_old)
                ->first();

                if(!$findProduct){
                    // SyncProductBizappJob::dispatch($user)->onQueue('syncProduct');
                    AddMissingProductFromBizappJob::dispatch($user, $request->productid)->onQueue('addMissingProductFromBizapp');

                    Log::error("stockUpdateFromBizapp v3: Product not found: pid={$request->pid}, domain={$request->domain}, productid={$request->productid}");
                    return response()->json([
                        'status' => '0',
                        'message' => 'No product with ' . $request->productid . 'ID found'
                    ]);
                }
                
                // Check if products is unlimited on POS & Bizapp
                // If true skip update table
                if($request->billstok === "-100" && $findProduct->product_stock === -100){
                    // Log that the update was skipped due to unlimited stock
                    Log::info('v3-stockUpdateFromBizapp', [
                        'action' => $action,
                        'stockStatus' => $stockStatus,
                        'user' => [
                            //'id' => $user->id,
                            'pid' => $user->pid,
                            'domain' => $user->domain,
                        ],
                        'product' => [
                            //'id' => $getProduct->id,
                            'product_id_bizapp' => $findProduct->product_id_bizapp,
                            'product_stock' => $findProduct->product_stock,
                            'product_stock_status' => $findProduct->product_stock_status,
                        ],
                        'updated_quantity' => $request->billstok,
                        'status' => 'stalled' 
                    ]);
                
                    return response()->json([
                        'status' => '1',
                        'message' => 'Product update skipped: stock marked as unlimited.'
                    ]);
                }
                
                // Ensure ACID compliance
                
                DB::beginTransaction(); // Start the transaction

                // Update product stocks
                Product::where([
                    // 'parent_company' => $parentCompanyId,
                    'product_id_bizapp' => $request->productid
                ])->update([
                    'product_stock' => (int)$request->billstok,
                    'product_stock_status' => $stockStatus
                ]);
                // Retrieve the updated product
                
                LogStock::create([
                    'company_id' => $parentCompanyId ?? '',
                    'user_id' => $user->id,
                    'product_id' => $findProduct->id,
                    'name' => $user->username ?? 'update from bizapp web',
                    'product_name' => $findProduct->product_name,
                    'product_sku' => $findProduct->product_SKU ?? "",
                    'quantity' => $request->billstok,
                    'remain' => $request->billstok,
                    'note' => 'Update from bizapp web',
                ]);
                
                DB::commit(); // Commit the transaction
                
                
                // Log::info("stockUpdateFromBizapp v3 SUCCESS : action={$request->action} , productid={$request->productid}, domain={$request->domain}");
                
                Log::info('v3-stockUpdateFromBizapp', [
                    'action' => $action,
                    'stockStatus' => $stockStatus,
                    'user' => [
                        //'id' => $user->id,
                        'pid' => $user->pid,
                        'domain' => $user->domain,
                    ],
                    'product' => [
                        //'id' => $getProduct->id,
                        'product_id_bizapp' => $findProduct->product_id_bizapp,
                        'product_stock' => $findProduct->product_stock,
                        'product_stock_status' => $findProduct->product_stock_status,
                    ],
                    'updated_quantity' => $request->billstok,
                    'status' => 'success' 
                ]);
                
                
                return response()->json([
                    'status' => '1',
                    'message' => 'Product updated successfully'
                ]);
            } catch (Exception $e) {
                
                DB::rollback(); // Rollback the transaction on failure
                
                Log::error("v3-stockUpdateFromBizapp Error : productid={$request->productid}, domain={$request->domain} , exception={$e->getMessage()}");
            }

        } else if ($request->action == 'delete'){
            try{
                ////////////////////////////
                // Retrieve products from DB
                $findProduct = Product::where('parent_company', $parentCompanyId)
                ->where(function ($query) use ($request) {
                    $query->where('product_id_bizapp', $request->productid)
                          ->orWhere('product_id_bizapp', $request->productid_old)
                          ->orWhere('product_SKU', $request->sku)
                          ->orWhere('product_SKU', $request->sku_old);
                })->delete();
                
                Log::info("stockUpdateFromBizapp v31 SUCCESS : productid={$request->productid}, domain={$request->domain}, action=delete");
                return response()->json([
                    'status' => '1',
                    'message' => 'Product deleted successfully'
                ]);
            } catch (Exception $e){
                Log::error("stockUpdateFromBizapp v31 ERROR : productid={$request->productid}, domain={$request->domain}, action=delete");
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to delete product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        } else if ($action === 'create'){
            Log::info("stockUpdateFromBizapp v31 action : create start");
            try{
                $user = User::where('pid',$request->pid)->where('domain',$request->domain)->first();
                if(!$user){
                    return response()->json([
                        'status' => '0',
                        'message' => 'User with ' . $request->pid . ' and ' . $request->domain .' not found'
                    ]);
                }
                ////////////////////////////
                // Retrieve products from DB
                $productExists = Product::where('product_id_bizapp', $request->productid)
                ->orWhere('product_id_bizapp',$request->productid_old)
                ->orWhere('product_SKU',$request->sku)
                ->orWhere('product_SKU',$request->sku_old)
                ->first();


                if(!$productExists){

                    sleep(1.5);
                    $urlPos = config('bizappos.bizappos_api_url');
                    $getProductDetailBizappAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                        'pid' => $request->pid,
                        'token' => 'aa',
                        'DOMAIN' => $request->domain,
                        'productid' => $request->productid
                    ])->throw()->json();
                    Log::info("stockUpdateFromBizapp v31 action={create} start " . json_encode($getProductDetailBizappAPI));
                    
                    if (!empty($getProductDetailBizappAPI) && isset($getProductDetailBizappAPI[0]['STATUS']) && $getProductDetailBizappAPI[0]['STATUS'] === '1') {
                        Log::info("stockUpdateFromBizapp v31 action={create} start 11" );
                        
                        $item = Product::create([
                            'parent_company' => $parentCompanyId,
                            'product_id_bizapp' => $getProductDetailBizappAPI[0]['id'],
                            'product_name' => $getProductDetailBizappAPI[0]['productname'],
                            'product_SKU' => $getProductDetailBizappAPI[0]['productsku'],
                            'product_brand' => $getProductDetailBizappAPI[0]['productbrand'] ?? 'TIADA BRAND',
                            'cost_price' => $getProductDetailBizappAPI[0]['costprice'],
                            'product_stock' => (int) $getProductDetailBizappAPI[0]['bilstok'],
                            'product_stock_status' => $getProductDetailBizappAPI[0]['statusstok'],
                            'product_price' => (float) $getProductDetailBizappAPI[0]['price'],
                            'product_weight' => $getProductDetailBizappAPI[0]['weight'],
                            'product_status' => $getProductDetailBizappAPI[0]['STATUS'],
                            'product_fav' => $getProductDetailBizappAPI[0]['productfav'] ?? '0'
                        ]);
                        Log::info("stockUpdateFromBizapp v31 action={create} start 22" );
                        
                        $item->productDetail()->create([
                            'product_attachment' => $getProductDetailBizappAPI[0]['attachment']
                        ]);
                        Log::info("stockUpdateFromBizapp v31 action={create} product added to bizappos " . $item->id);
                        return response()->json([
                            'status' => '1',
                            'message' => 'Product added to Bizappos'
                        ]);
                    } else {
                        Log::info("stockUpdateFromBizapp v31 action={create} product info is null " . json_encode($getProductDetailBizappAPI));
                        return response()->json([
                            'status' => '0',
                            'message' => 'Bizapp get product info return null'
                        ]);
                    }
                   
                } else {
                    return response()->json([
                        'status' => '0',
                        'message' => 'Productid ' . $request->productid . 'already exists in database'
                    ]);
                }
            } catch (Exception $e){
                Log::error("stockUpdateFromBizapp v31 Error : productid={$request->productid}, domain={$request->domain}, action=create, exception={$e->getMessage()}");
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to create product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        } else if ($action === 'updateproduct'){
            Log::info("stockUpdateFromBizapp v31 action : updateproduct start");
            try{
                $user = User::where('pid',$request->pid)->where('domain',$request->domain)->first();
                if(!$user){
                    return response()->json([
                        'status' => '0',
                        'message' => 'User with ' . $request->pid . ' and ' . $request->domain .' not found'
                    ]);
                }
                $latestProduct = Product::where('parent_company', $parentCompanyId)
                                        ->where(function ($query) use ($request) {
                                            $query->where('product_id_bizapp', $request->productid)
                                                ->orWhere('product_id_bizapp', $request->productid_old)
                                                ->orWhere('product_SKU', $request->sku)
                                                ->orWhere('product_SKU', $request->sku_old);
                                        })->first();
                

                if(!$latestProduct){
                    Log::info("stockUpdateFromBizapp v31 : Product not found" );
                    
                    return response()->json([
                        'status' => '1',
                        'message' => 'Product id and sku not found, please check with Bizapp admins'
                    ]);

                } else {
                    Log::info("stockUpdateFromBizapp v31 : Product FOUND" );
                    // start debug below here
                    $urlPos = config('bizappos.bizappos_api_url');
                    $getProductDetailBizappAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                        'pid' => $request->pid,
                        'token' => 'aa',
                        'DOMAIN' => $request->domain,
                        'productid' => $request->productid
                    ])->throw()->json();
                    
                    if (!empty($getProductDetailBizappAPI) && isset($getProductDetailBizappAPI[0]['STATUS']) && $getProductDetailBizappAPI[0]['STATUS'] === '1'){
                        // Update the latest product
                        $latestProduct->update([
                            'product_name' => $getProductDetailBizappAPI[0]['productname'],
                            'product_SKU' => $getProductDetailBizappAPI[0]['productsku'],
                            'product_brand' => $getProductDetailBizappAPI[0]['productbrand'] ?? 'TIADA BRAND',
                            'cost_price' => $getProductDetailBizappAPI[0]['costprice'],
                            'product_stock' => (int) $getProductDetailBizappAPI[0]['bilstok'],
                            'product_stock_status' => $getProductDetailBizappAPI[0]['statusstok'],
                            'product_price' => (float) $getProductDetailBizappAPI[0]['price'],
                            'product_weight' => $getProductDetailBizappAPI[0]['weight'],
                            'product_status' => $getProductDetailBizappAPI[0]['STATUS'],
                            'product_fav' => $getProductDetailBizappAPI[0]['productfav'] ?? '0'
                        ]);

                        $item = $latestProduct; // Reference for further processing
                        $item->productDetail()->update([
                            'product_attachment' => $getProductDetailBizappAPI[0]['attachment']
                        ]);

                        Log::info("stockUpdateFromBizapp v31 : Product updated successfully");

                        return response()->json([
                            'status' => '1',
                            'message' => 'Product updated successfully'
                        ]);

                    } else {
                        Log::info("stockUpdateFromBizapp v31 action={productupdate} product info is null " . json_encode($getProductDetailBizappAPI));
                        return response()->json([
                            'status' => '0',
                            'message' => 'Bizapp get product info return null'
                        ]);
                    }

                } 
            } catch (Exception $e){
                Log::error("stockUpdateFromBizapp v31 Error : productid={$request->productid}, domain={$request->domain}, action=updateproduct, exception={$e->getMessage()}");
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to update product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        }
    }


    public function getReceiptImage(Request $request)
    {
    
    // Validate input
    $validator = Validator::make($request->all(), [
        'order_id' => 'required|exists:orders,bizapp_temp_id',
        'image' => 'required|file|mimes:jpg,jpeg,png|max:2048', // Adjust the file size limit as needed
    ]);

    if ($validator->fails()) {
        Log::info("Save image failed : Validation error - " . json_encode($validator->errors()));
        return response()->json([
            'status' => '0',
            'message' => 'Validation error',
            'errors' => $validator->errors()
        ], 422);
    }

    $user = auth()->user();
    if(!$user){
        Log::info("Save image failed : User not found - {$request->order_id}");
        return response()->json([
            'status' => '0',
            'message' => 'User not found'
        ], 401);
    }

    try {
        $order = Order::where('bizapp_temp_id',$request->order_id)->first();

        // Check if the order belongs to the authenticated user
        if ($order->user_id !== $user->id) {
            Log::info("Save image failed : Unauthorized access - User ID: {$user->id}, Order ID: {$request->order_id}");
            return response()->json([
                'status' => '0',
                'message' => 'Unauthorized access to this order'
            ], 403);
        }

        // Save the image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $path = storage_path('app/public/images');

            // Create the directory if it doesn't exist
            if (!is_dir($path)) {
                mkdir($path, 0775, true);
            }

            // Move the file to the storage directory
            $image->move($path, $filename);

            // Save the image path in the order details or as needed
            $order->update([
                'order_number' => $filename
            ]);

            Log::info("Image saved successfully for order ID: {$request->order_id}");
            return response()->json([
                'status' => '1',
                'message' => 'Image saved successfully'
            ], 200);
        } else {
            return response()->json([
                'status' => '0',
                'message' => 'No image file provided'
            ], 400);
        }
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
        Log::info("Save image failed : Order not found - {$request->order_id}");
        return response()->json([
            'status' => '0',
            'message' => 'Order not found'
        ], 404);
    } catch (\Exception $e) {
        Log::error("Save image failed : {$e->getMessage()}");
        return response()->json([
            'status' => '0',
            'message' => 'Failed to save image'
        ], 500);
    }
    }


    public function stockUpdateFromBizappV31(Request $request)
    {
        if (strtolower($request->header('Content-Type')) !== 'application/json' && strtolower($request->header('Content-Type')) !== 'application/json; charset= utf-8') {
            Log::channel('bulk_update_bizapp')->error("Invalid Content-Type header: " . $request->header('Content-Type'));
            return response()->json([
                'status' => '0',
                'message' => 'Invalid Content-Type. Expected application/json.'
            ], 415);
        }
        
        if($request->action != 'updatestock' && $request->action != 'delete' && $request->action != 'create' && $request->action != 'updateproduct'){
            Log::channel('stock_update')->error("Action data is empty", [
                'request' => $request->all()
            ]);
            return response()->json([
                'status' => '0',
                'message' => 'action data is empty, please specify (updatestock/delete/create)'
            ]);
        }

        // check if the billstok is in the correct integer format
       if ($request->action == 'updatestock' && !preg_match('/^-?\d+$/', $request->billstok)) {
            Log::channel('stock_update')->error("billstok is not in correct integer format");
            return response()->json([
                'status' => '0',
                'message' => 'billstok must be a whole number in integer format'
            ]);
        }

        // Retrive User info from Cache. If not exist callback retrive from DB and add to Cache. 
        $user = Cache::remember("user:{$request->pid}:{$request->domain}", now()->addHours(1), function () use ($request) {
            return User::select('id', 'pid', 'domain', 'username')->where('pid', $request->pid)->where('domain', $request->domain)->first();
        });

        if (!$user) {
            // Check if username_bizapp exists and is not empty in the request
            $usernameBizapp = $request->filled('username_bizapp') ? $request->username_bizapp : null;
        
            if ($usernameBizapp) {
                try {
                    Log::channel('stock_update')->warning("User not found: pid={$request->pid}, domain={$request->domain}, sending to queue for registration");
                    RegisterBizappUserJob::dispatch($request->domain, $usernameBizapp, config('bizappos.bizapp_mpw'))->onQueue('registerUserBizapp');
                    return response()->json([
                        'status' => '0',
                        'message' => 'User not found in bizappos, sending to queue for registration...'
                    ]);
                } catch (\Exception $e) {
                    Log::channel('stock_update')->error("Failed to dispatch registration job for user: pid={$request->pid}, domain={$request->domain}. Error: " . $e->getMessage());
                    return response()->json([
                        'status' => '0',
                        'message' => 'User not found in bizappos, but failed to queue registration. Please try again later.'
                    ], 500);
                }
            } else {
                Log::channel('stock_update')->error("Username_bizapp not found or empty in request for user: pid={$request->pid}, domain={$request->domain}. Please specify username_bizapp in the request");
                return response()->json([
                    'status' => '0',
                    'message' => 'User not found in bizappos, unable to proceed with registration as there is no bizapp_username provided'
                ]);
            }
        }
        
        // Retrive Company info from Cache. If not exist callback retrive from DB and add to Cache. 
        $company = Cache::remember("company:user:{$user->id}", now()->addHours(1), function () use ($user) {
            return Company::select('id')->where('user_id', $user->id)->first();
        });
        
        if (!$company) {
        // Cache employee and branch data
        $employeeAndBranch = Cache::remember("employee_branch:user:{$user->id}", now()->addHours(1), function () use ($user) {
        $employee = Employee::select('id')->where('user_id', $user->id)->first();
        if ($employee) {
            $branch = CompanyBranch::select('branch_of')->where('person_in_charge', $employee->id)->first();
            if ($branch) {
                return ['branch_of' => $branch->branch_of];
                }
            }
        });

        if (!$employeeAndBranch) {
            Log::error("stockUpdateFromBizapp v31: No company or branch found for user: {$user->id}");
            return response()->json([
                'status' => '0',
                'message' => 'No associated company or branch found'
            ]);
        }

            $parentCompanyId = $employeeAndBranch['branch_of'];
        } else {
            $parentCompanyId = $company->id;
        }

        Log::channel('stock_update')->info("stockUpdateFromBizapp v31 START : " . json_encode($request->all()));

       
        
        // Extract 'action' parameter
        $action = $request->action;
        // Define stock status
        $stockStatus = $request->billstok == -100 ? 'Y' : 'N';
        // Log::info('v3-stockUpdateFromBizapp [/action : ' . $action. ']');
        
        
        // More informative Log format
        Log::channel('stock_update')->info("Stock update initiated", [
            'action' => $request->action,
            'stockStatus' => $request->billstok == -100 ? 'Y' : 'N',
            'user' => [
                'pid' => $request->pid,
                'domain' => $request->domain,
            ],
            'product' => [
                'product_id_bizapp' => $request->productid,
                'product_stock' => $request->billstok,
            ],
        ]);
        
        if($request->action == 'updatestock'){
            // Update or create product
            try{
                ////////////////////////////
                // Retrieve products from DB
                $findProduct = Product::where('parent_company', $parentCompanyId)
                ->where(function ($query) use ($request) {
                    $query->where('product_id_bizapp', $request->productid)
                        ->orWhere('product_id_bizapp', $request->productid_old)
                        ->orWhere('product_SKU', $request->sku)
                        ->orWhere('product_SKU', $request->sku_old);
                })
                ->first();

                if(!$findProduct){
                    AddMissingProductFromBizappJob::dispatch($user, $request->productid)->onQueue('addMissingProductFromBizapp');

                    Log::channel('stock_update')->error("updatestock :Product not found: pid={$request->pid}, domain={$request->domain}, productid={$request->productid}");
                    return response()->json([
                        'status' => '0',
                        'message' => 'Product id and sku not found, triggered sync product job'
                    ]);
                }
                
                // Check if products is unlimited on POS & Bizapp
                // If true skip update table
                if($request->billstok === "-100" && $findProduct->product_stock === -100){
                    // Log that the update was skipped due to unlimited stock
                    Log::info('v31-stockUpdateFromBizapp', [
                        'action' => 'updatestock',
                        'stockStatus' => $stockStatus,
                        'user' => [
                            //'id' => $user->id,
                            'pid' => $user->pid,
                            'domain' => $user->domain,
                        ],
                        'product' => [
                            //'id' => $getProduct->id,
                            'product_id_bizapp' => $findProduct->product_id_bizapp,
                            'product_stock' => $findProduct->product_stock,
                            'product_stock_status' => $findProduct->product_stock_status,
                        ],
                        'updated_quantity' => $request->billstok,
                        'status' => 'stalled' 
                    ]);
                
                    return response()->json([
                        'status' => '1',
                        'message' => 'Product update skipped: stock marked as unlimited.'
                    ]);
                }
                
                // Ensure ACID compliance
                
                DB::beginTransaction(); // Start the transaction

                // Update product stocks
                Product::where([
                    // 'parent_company' => $parentCompanyId,
                    'product_id_bizapp' => $request->productid
                ])->update([
                    'product_stock' => (int)$request->billstok,
                    'product_stock_status' => $stockStatus
                ]);
                // Retrieve the updated product
                
                LogStock::create([
                    'company_id' => $parentCompanyId ?? '',
                    'user_id' => $user->id,
                    'product_id' => $findProduct->id,
                    'name' => $user->username ?? 'update from bizapp web',
                    'product_name' => $findProduct->product_name,
                    'product_sku' => $findProduct->product_SKU ?? "",
                    'quantity' => $request->billstok,
                    'remain' => $request->billstok,
                    'note' => 'Update from bizapp web',
                ]);
                
                DB::commit(); // Commit the transaction
                
                
                // Log::info("stockUpdateFromBizapp v3 SUCCESS : action={$request->action} , productid={$request->productid}, domain={$request->domain}");
                Log::channel('stock_update')->info("Stock update initiated", [
                    'action' => $action,
                    'stockStatus' => $stockStatus,
                    'user' => [
                        //'id' => $user->id,
                        'pid' => $user->pid,
                        'domain' => $user->domain,
                    ],
                    'product' => [
                        //'id' => $getProduct->id,
                        'product_id_bizapp' => $findProduct->product_id_bizapp,
                        'product_stock' => $findProduct->product_stock,
                        'product_stock_status' => $findProduct->product_stock_status,
                    ],
                    'updated_quantity' => $request->billstok,
                    'status' => 'success' 
                ]);
                
                
                return response()->json([
                    'status' => '1',
                    'message' => 'Product updated successfully'
                ]);
            } catch (Exception $e) {
                
                DB::rollback(); // Rollback the transaction on failure
                
                Log::channel('stock_update')->error("Update stock error :", [
                    'productid: ' => $request->productid,
                    'domain: ' => $request->domain,
                    'exception: ' => $e->getMessage()
                ]);
            }

        } else if ($request->action == 'delete'){
            try{
                ////////////////////////////
                // Retrieve products from DB
                $findProduct = Product::where('parent_company', $parentCompanyId)
                ->where(function ($query) use ($request) {
                    $query->where('product_id_bizapp', $request->productid)
                          ->orWhere('product_id_bizapp', $request->productid_old)
                          ->orWhere('product_SKU', $request->sku)
                          ->orWhere('product_SKU', $request->sku_old);
                })->delete();
                
                Log::channel('stock_update')->info("delete product success :", [
                    'productid: ' => $request->productid,
                    'domain: ' => $request->domain,
                    'action: ' => 'delete',
                ]);
                return response()->json([
                    'status' => '1',
                    'message' => 'Product deleted successfully'
                ]);
            } catch (Exception $e){
                Log::channel('stock_update')->error("delete product error :", [
                    'productid: ' => $request->productid,
                    'domain: ' => $request->domain,
                    'action: ' => 'delete',
                    'exception: ' => $e->getMessage()
                ]);
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to delete product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        } else if ($action === 'create'){
            Log::channel('stock_update')->info("create product start received request", [
                'request' => json_encode($request->all())
            ]);
            try{
                $user = User::where('pid',$request->pid)->where('domain',$request->domain)->first();
                if(!$user){
                    Log::channel('stock_update')->error("create product error :", [
                        'message' => 'User with ' . $request->pid . ' and ' . $request->domain .' not found'
                    ]);
                    return response()->json([
                        'status' => '0',
                        'message' => 'User with ' . $request->pid . ' and ' . $request->domain .' not found'
                    ]);
                }
                ////////////////////////////
                // Retrieve products from DB
                $productExists = Product::where('parent_company', $parentCompanyId)
                    ->where(function ($query) use ($request) {
                        $query->where('product_id_bizapp', $request->productid)
                            ->orWhere('product_id_bizapp', $request->productid_old)
                            ->orWhere('product_SKU', $request->sku)
                            ->orWhere('product_SKU', $request->sku_old);
                    })
                    ->first();


                if(!$productExists){

                    sleep(1.5);
                    $urlPos = config('bizappos.bizappos_api_url');
                    $getProductDetailBizappAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                        'pid' => $request->pid,
                        'token' => 'aa',
                        'DOMAIN' => $request->domain,
                        'productid' => $request->productid
                    ])->throw()->json();
                    Log::channel('stock_update')->info("create product start get product info from bizappos", [
                        'request' => json_encode($getProductDetailBizappAPI)
                    ]);
                    
                    if (!empty($getProductDetailBizappAPI) && isset($getProductDetailBizappAPI[0]['STATUS']) && $getProductDetailBizappAPI[0]['STATUS'] === '1') {
                        
                        $item = Product::create([
                            'parent_company' => $parentCompanyId,
                            'product_id_bizapp' => $getProductDetailBizappAPI[0]['id'],
                            'product_name' => $getProductDetailBizappAPI[0]['productname'],
                            'product_SKU' => $getProductDetailBizappAPI[0]['productsku'],
                            'product_brand' => $getProductDetailBizappAPI[0]['productbrand'] ?? 'TIADA BRAND',
                            'cost_price' => $getProductDetailBizappAPI[0]['costprice'],
                            'product_stock' => (int) $getProductDetailBizappAPI[0]['bilstok'],
                            'product_stock_status' => $getProductDetailBizappAPI[0]['statusstok'],
                            'product_price' => (float) $getProductDetailBizappAPI[0]['price'],
                            'product_weight' => $getProductDetailBizappAPI[0]['weight'],
                            'product_status' => $getProductDetailBizappAPI[0]['STATUS'],
                            'product_fav' => $getProductDetailBizappAPI[0]['productfav'] ?? '0'
                        ]);
                        
                        $item->productDetail()->create([
                            'product_attachment' => $getProductDetailBizappAPI[0]['attachment']
                        ]);
                        Log::channel('stock_update')->info("product added to bizappos", [
                            'product_id' => $item->id
                        ]);
                        return response()->json([
                            'status' => '1',
                            'message' => 'Product added to Bizappos'
                        ]);
                    } else {
                        Log::channel('stock_update')->error("product info is null", [
                            'data' => json_encode($getProductDetailBizappAPI)
                        ]);
                        return response()->json([
                            'status' => '0',
                            'message' => 'Bizapp get product info return null'
                        ]);
                    }
                   
                } else {
                    Log::channel('stock_update')->error("product create already exists : ", [
                        'productid: ' => $request->productid,
                    ]);
                    return response()->json([
                        'status' => '0',
                        'message' => 'Productid ' . $request->productid . 'already exists in database'
                    ]);
                }
            } catch (Exception $e){
                Log::channel('stock_update')->error("product create exception error : ", [
                    'productid: ' => $request->productid,
                    'domain: ' => $request->domain,
                    'exception: ' => $e->getMessage()
                ]);
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to create product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        } else if ($action === 'updateproduct'){
            Log::channel('stock_update')->info("update product start received request", [
                'request' => json_encode($request->all())
            ]);
            try{
                $user = User::where('pid',$request->pid)->where('domain',$request->domain)->first();
                if(!$user){
                    return response()->json([
                        'status' => '0',
                        'message' => 'User with ' . $request->pid . ' and ' . $request->domain .' not found'
                    ]);
                }
                $latestProduct = Product::where('parent_company', $parentCompanyId)
                                        ->where(function ($query) use ($request) {
                                            $query->where('product_id_bizapp', $request->productid)
                                                ->orWhere('product_id_bizapp', $request->productid_old)
                                                ->orWhere('product_SKU', $request->sku)
                                                ->orWhere('product_SKU', $request->sku_old);
                                        })->first();
                

                if(!$latestProduct){
                    Log::channel('stock_update')->error("update product not found, triggered addMissingProductJob", [
                        'request' => json_encode($request->all())
                    ]);
                    // SyncProductBizappJob::dispatch($user)->onQueue('syncProduct');
                    AddMissingProductFromBizappJob::dispatch($user, $request->productid)->onQueue('addMissingProductFromBizapp');

                    return response()->json([
                        'status' => '1',
                        'message' => 'Product id and sku not found, triggered sync product job'
                    ]);

                } else {
                    Log::channel('stock_update')->info("update product start");
                    // start debug below here
                    $urlPos = config('bizappos.bizappos_api_url');
                    $getProductDetailBizappAPI = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PRODUCT_INFO',[
                        'pid' => $request->pid,
                        'token' => 'aa',
                        'DOMAIN' => $request->domain,
                        'productid' => $request->productid
                    ])->throw()->json();
                    
                    if (!empty($getProductDetailBizappAPI) && isset($getProductDetailBizappAPI[0]['STATUS']) && $getProductDetailBizappAPI[0]['STATUS'] === '1'){
                        // Update the latest product
                        $latestProduct->update([
                            'product_name' => $getProductDetailBizappAPI[0]['productname'],
                            'product_SKU' => $getProductDetailBizappAPI[0]['productsku'],
                            'product_brand' => $getProductDetailBizappAPI[0]['productbrand'] ?? 'TIADA BRAND',
                            'cost_price' => $getProductDetailBizappAPI[0]['costprice'],
                            'product_stock' => (int) $getProductDetailBizappAPI[0]['bilstok'],
                            'product_stock_status' => $getProductDetailBizappAPI[0]['statusstok'],
                            'product_price' => (float) $getProductDetailBizappAPI[0]['price'],
                            'product_id_bizapp' => $getProductDetailBizappAPI[0]['id'],
                            'product_weight' => $getProductDetailBizappAPI[0]['weight'],
                            'product_status' => $getProductDetailBizappAPI[0]['STATUS'],
                            'product_fav' => $getProductDetailBizappAPI[0]['productfav'] ?? '0'
                        ]);

                        $item = $latestProduct; // Reference for further processing
                        $item->productDetail()->update([
                            'product_attachment' => $getProductDetailBizappAPI[0]['attachment']
                        ]);

                        Log::channel('stock_update')->info("update product success");
                        return response()->json([
                            'status' => '1',
                            'message' => 'Product updated successfully'
                        ]);

                    } else {
                        Log::channel('stock_update')->error("update product info is null", [
                            'request' => json_encode($getProductDetailBizappAPI)
                        ]);
                        return response()->json([
                            'status' => '0',
                            'message' => 'Bizapp get product info return null'
                        ]);
                    }

                } 
            } catch (Exception $e){
                Log::channel('stock_update')->error("update product main exception : ", [
                    'productid: ' => $request->productid,
                    'domain: ' => $request->domain,
                    'exception: ' => $e->getMessage()
                ]);
                return response()->json([
                    'status' => '0',
                    'message' => 'failed to update product '. $request->productid,
                    'exception' => $e->getMessage()
                ]);
            }

        }
    }

    public function stockUpdateFromBizappV31Bulk(Request $request)
    {
        if (strtolower($request->header('Content-Type')) !== 'application/json' && strtolower($request->header('Content-Type')) !== 'application/json; charset= utf-8') {
            Log::channel('bulk_update_bizapp')->error("Invalid Content-Type header: " . $request->header('Content-Type'));
            return response()->json([
                'status' => '0',
                'message' => 'Invalid Content-Type. Expected application/json.'
            ], 415);
        }

        // Validate bulk request structure
        $validator = Validator::make($request->all(), [
            'pid' => 'required|string',
            'domain' => 'required|string',
            'action' => 'required|in:updatestock,delete,create,updateproduct',
            'productinfo' => 'required|array',
            'productinfo.*.productid' => 'string',
            'productinfo.*.productid_old' => 'string',
            'productinfo.*.sku' => 'string',
            'productinfo.*.sku_old' => 'string',
            'productinfo.*.billstok' => 'integer'
        ]);

        if ($validator->fails()) {
            Log::channel('bulk_update_bizapp')->error("Bulk validation failed: " . json_encode($validator->errors()));
            return response()->json([
                'status' => '0',
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Cache user and company data (same for all items)
        $user = Cache::remember("user:{$request->pid}:{$request->domain}", now()->addHours(1), function () use ($request) {
            return User::select('id', 'pid', 'domain','username')
                ->where('pid', $request->pid)
                ->where('domain', $request->domain)
                ->first();
        });

        if (!$user) {
            // Check if username_bizapp exists and is not empty in the request
            $usernameBizapp = $request->filled('username_bizapp') ? $request->username_bizapp : null;
        
            if ($usernameBizapp) {
                try {
                    Log::channel('bulk_update_bizapp')->warning("User not found: pid={$request->pid}, domain={$request->domain}, sending to queue for registration");
                    RegisterBizappUserJob::dispatch($request->domain, $usernameBizapp, config('bizappos.bizapp_mpw'))->onQueue('registerUserBizapp');
                    return response()->json([
                        'status' => '0',
                        'message' => 'User not found in bizappos, sending to queue for registration...'
                    ]);
                } catch (\Exception $e) {
                    Log::channel('bulk_update_bizapp')->error("Failed to dispatch registration job for user: pid={$request->pid}, domain={$request->domain}. Error: " . $e->getMessage());
                    return response()->json([
                        'status' => '0',
                        'message' => 'User not found in bizappos, but failed to queue registration. Please try again later.'
                    ], 500);
                }
            } else {
                Log::channel('bulk_update_bizapp')->error("Username_bizapp not found or empty in request for user: pid={$request->pid}, domain={$request->domain}. Please specify username_bizapp in the request");
                return response()->json([
                    'status' => '0',
                    'message' => 'User not found in bizappos, unable to proceed with registration as there is no bizapp_username provided'
                ]);
            }
        }

        $company = Cache::remember("company:user:{$user->id}", now()->addHours(1), function () use ($user) {
            return Company::select('id')->where('user_id', $user->id)->first();
        });

        $parentCompanyId = $company ? $company->id : $this->getParentCompanyId($user);

        try {
            $processed = 0;
            $errors = [];
            
            foreach ($request->productinfo as $index => $productData) {
                try {
                    DB::transaction(function () use ($productData, $user, $parentCompanyId, $request) {
                        $findProduct = Product::where('product_id_bizapp', $productData['productid'])
                            ->orWhere('product_id_bizapp', $productData['productid_old'])
                            ->orWhere('product_SKU', $productData['sku'])
                            ->orWhere('product_SKU', $productData['sku_old'])
                            ->first();

                        if (!$findProduct) {
                            AddMissingProductFromBizappJob::dispatch($user, $productData['productid'])
                                ->onQueue('addMissingProductFromBizapp');
                            
                            Log::channel('bulk_update_bizapp')->error(
                                'stockUpdateFromBizappV31Bulk: Product not found. Sending to addMissingProductFromBizapp queue. ' ,
                                [
                                    'pid' => $request->pid,
                                    'domain' => $request->domain,
                                    'productid' => $productData['productid'],
                                ]
                            );
                            return response()->json([
                                'status' => '0',
                                'message' => 'Product id and sku not found, triggered sync product job'
                            ]);
                            
                        }

                        if ($request->action === 'updateproduct') {
                            $stockStatus = $productData['billstok'] == -100 ? 'Y' : 'N';
                            
                            Product::where([
                                'parent_company' => $parentCompanyId,
                                'product_id_bizapp' => $productData['productid']
                            ])->update([
                                'product_stock' => (int)$productData['billstok'],
                                'product_stock_status' => $stockStatus
                            ]);

                            LogStock::create([
                                'company_id' => $parentCompanyId,
                                'user_id' => $user->id,
                                'product_id' => $findProduct->id,
                                'name' => $user->username ?? 'Bulk update from bizapp',
                                'product_name' => $findProduct->product_name,
                                'product_sku' => $findProduct->product_SKU,
                                'quantity' => $productData['billstok'],
                                'remain' => $productData['billstok'],
                                'note' => 'Bulk stock update'
                            ]);
                        } elseif ($request->action === 'updatestock') {
                            $this->handleBulkStockUpdate($productData, $findProduct, $parentCompanyId, $user);
                        } elseif ($request->action === 'delete') {
                            $this->handleBulkDelete($productData, $findProduct, $parentCompanyId, $user);
                        } elseif ($request->action === 'create') {
                            $this->handleBulkCreate($productData, $parentCompanyId, $user);
                        }
                    });
                    
                    $processed++;
                } catch (\Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'productid' => $productData['productid'],
                        'error' => $e->getMessage()
                    ];
                    Log::channel('bulk_update_bizapp')->error("Bulk update failed for {$productData['productid']}: {$e->getMessage()}");
                    return response()->json([
                        'status' => '0',
                        'messsage' => $errors
                    ]);
                }
            }

            Log::channel('bulk_update_bizapp')->info("Bulk update processed", [
                'status' => '1',
                'message' => 'Bulk update processed',
                'pid' => $user->pid,
                'username' => $user->username,
                'processed' => $processed,
                'errors' => $errors
            ]);
            
            return response()->json([
                'status' => '1',
                'message' => 'Bulk update processed',
                'processed' => $processed,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::channel('bulk_update_bizapp')->error("Bulk update fatal error:",[
                'status' => '0',
                'message' => $e->getMessage(),
                'action' => $request->action,
                'pid' => $user->pid,
                'username' => $user->username,
            ]);
            return response()->json([
                'status' => '0',
                'message' => 'Bulk processing failed'
            ], 500);
        }
    }

    private function getParentCompanyId(User $user): int
    {
        $employee = Employee::where('user_id', $user->id)->first();
        if ($employee) {
            $branch = CompanyBranch::where('person_in_charge', $employee->id)->first();
            return $branch->branch_of ?? throw new \Exception('No parent company found');
        }
        throw new \Exception('No company association found');
    }

    private function handleBulkStockUpdate($productData, $product, $parentCompanyId, $user): void
    {
        $newStock = (int)$productData['billstok'];
        $oldStock = $product->product_stock;
        
        $product->update([
            'product_stock' => $newStock,
            'product_stock_status' => $newStock > 0 ? 'Y' : 'N'
        ]);

        LogStock::create([
            'company_id' => $parentCompanyId,
            'user_id' => $user->id,
            'product_id' => $product->id,
            'name' => $user->username ?? 'Bulk stock update',
            'product_name' => $product->product_name,
            'product_sku' => $product->product_SKU,
            'quantity' => $newStock - $oldStock,
            'remain' => $newStock,
            'note' => 'Bulk stock adjustment'
        ]);
    }

    private function handleBulkDelete($productData, $product, $parentCompanyId, $user): void
    {
        // Verify product exists even if soft-deleted
        if ($product->trashed()) {
            throw new \Exception('Product already deleted');
        }

        if ($product->product_id_bizapp !== $productData['productid']) {
            throw new \Exception('Product ID mismatch for deletion');
        }

        DB::transaction(function () use ($product, $productData) {
            // Perform actual soft delete
            $product->delete(); // This sets the deleted_at column
            
            // Update status for business logic
            $product->update(['product_status' => 'deleted']);
        });

        Log::channel('bulk_update_bizapp')->info("Product soft-deleted", [
            'product_id' => $product->id,
            'bizapp_id' => $productData['productid'],
            'deleted_at' => now()->toDateTimeString()
        ]);
    }

    private function handleBulkCreate($productData, $parentCompanyId, $user): void
    {
        $existing = Product::where('parent_company', $parentCompanyId)
        ->where(function ($query) use ($productData) {
            $query->where('product_id_bizapp', $productData['productid'])
                ->orWhere('product_id_bizapp', $productData['productid_old'])
                ->orWhere('product_SKU', $productData['sku'])
                ->orWhere('product_SKU', $productData['sku_old']);
        })
        ->exists();

        // Add validation for special cases
        if ($existing) {
            throw new \Exception("Product already exists with matching SKU or Bizapp ID in company {$parentCompanyId}");
        }

        $newProduct = Product::create([
            'parent_company' => $parentCompanyId,
            'product_id_bizapp' => $productData['productid'],
            'product_SKU' => $productData['sku'],
            'product_name' => 'New Product - Pending Sync',
            'product_stock' => $productData['billstok'],
            'product_stock_status' => $productData['billstok'] > 0 ? 'Y' : 'N',
            'product_status' => 'pending'
        ]);

        // SyncSingleProductFromBizappJob::dispatch($user, $newProduct->product_id_bizapp)
        //     ->onQueue('highPrioritySync');
    }

    /**
     * Generate PDF receipt for an order (Public Access - No Authentication Required)
     *
     * @param string $orderId
     * @return \Illuminate\Http\Response
     */
    public function generatePDFPublic($orderId)
    {
        try {
            // Find the order with order details and related data
            $order = Order::with(['orderDetails', 'user.userDetails', 'company'])
                ->where('id', $orderId)
                ->first();

            if (!$order) {
                Log::error("Generate PDF Public failed: Order not found - {$orderId}");
                return response()->json([
                    'status' => '0',
                    'message' => 'Order not found'
                ], 404);
            }

            // Get company information
            $company = null;
            $companyLogo = null;
            
            // Try to get company from order relationship first
            if ($order->company) {
                $companyModel = $order->company;
                $company = (object) [
                    'id' => $companyModel->id,
                    'com_name' => $companyModel->com_name,
                    'com_registration_no' => $companyModel->com_registration_no,
                    'com_sst_number' => $companyModel->com_sst_number,
                    'com_address' => $companyModel->com_address,
                    'com_postcode' => $companyModel->com_postcode,
                    'state_name' => $companyModel->com_state,
                    'com_mobile' => $companyModel->com_mobile,
                    'com_email' => $companyModel->com_email
                ];
                
                // Get company logo from receipts table
                $receipt = Receipt::where('company_id', $companyModel->id)->first();
                if ($receipt && $receipt->logo) {
                    try {
                        $logoContent = Storage::disk('s3')->get($receipt->logo);
                        $companyLogo = 'data:image/' . pathinfo($receipt->logo, PATHINFO_EXTENSION) . ';base64,' . base64_encode($logoContent);
                    } catch (\Exception $e) {
                        Log::warning("Failed to load company logo from S3: {$e->getMessage()}", [
                            'logo_path' => $receipt->logo,
                            'company_id' => $companyModel->id
                        ]);
                    }
                }
            } else {
                // Fallback: try to get company through user relationship
                if ($order->user) {
                    $userCompany = Company::where('user_id', $order->user->id)->first();
                    if ($userCompany) {
                        $company = (object) [
                            'id' => $userCompany->id,
                            'com_name' => $userCompany->com_name,
                            'com_registration_no' => $userCompany->com_registration_no,
                            'com_sst_number' => $userCompany->com_sst_number,
                            'com_address' => $userCompany->com_address,
                            'com_postcode' => $userCompany->com_postcode,
                            'state_name' => $userCompany->com_state,
                            'com_mobile' => $userCompany->com_mobile,
                            'com_email' => $userCompany->com_email
                        ];
                        
                        // Get company logo
                        $receipt = Receipt::where('company_id', $userCompany->id)->first();
                        if ($receipt && $receipt->logo) {
                            try {
                                $logoContent = Storage::disk('s3')->get($receipt->logo);
                                $companyLogo = 'data:image/' . pathinfo($receipt->logo, PATHINFO_EXTENSION) . ';base64,' . base64_encode($logoContent);
                            } catch (\Exception $e) {
                                Log::warning("Failed to load company logo from S3: {$e->getMessage()}", [
                                    'logo_path' => $receipt->logo,
                                    'company_id' => $userCompany->id
                                ]);
                            }
                        }
                    }
                }
            }

            if (!$company) {
                Log::error("Generate PDF Public failed: Company information not found for order: {$orderId}");
                return response()->json([
                    'status' => '0',
                    'message' => 'Company information not found'
                ], 404);
            }

            // Get currency from user details or default
            $currency = $order->user?->userDetails?->currency ?? 'RM';

            // Generate PDF using the existing receipt template
            $pdf = Pdf::loadView('backend.order.receipt-pdf', [
                'order' => $order,
                'user' => $order->user,
                'company' => $company,
                'currency' => $currency,
                'companyLogo' => $companyLogo
            ])
            ->setOption('isPhpEnabled', true)
            ->setOption('isRemoteEnabled', true)
            ->setPaper('a4', 'portrait');

            Log::info("PDF receipt generated successfully (public) for order: {$orderId}");

            // Return PDF as download response
            $filename = "receipt-{$order->order_number}-" . date('YmdHis') . ".pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error("Generate PDF Public failed: {$e->getMessage()}", [
                'order_id' => $orderId,
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => '0',
                'message' => 'Failed to generate PDF receipt'
            ], 500);
        }
    }

}
