<?php

namespace App\Http\Controllers\API\v3;

use Carbon\Carbon;
use App\Models\MoneyOutRecord;
use App\Models\MoneyOutSubcategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\MoneyOutCategory;
use App\Models\Company;
use App\Models\Receipt;
use Barryvdh\DomPDF\Facade\Pdf;

class MoneyOutController extends Controller
{
  /**
     * Get form data for creating a new money out record
     */
    public function index()
    {
        try {
            $user = getUser();
            
            // Get categories with their subcategories, grouped by type (COGS and EXPENSES)
            $categories = MoneyOutCategory::with(['subcategories' => function($query) use ($user) {
                $query->where('company_id', $user['company']['id'])
                    ->orderBy('name');
            }])
            ->where('company_id', $user['company']['id'])
            ->orderBy('type')
            ->orderBy('name')
            ->get()
            ->groupBy('type');

            // Format the response
            $formattedCategories = [];
            foreach ($categories as $type => $typeCategories) {
                $formattedCategories[$type] = $typeCategories->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'type' => $category->type,
                        'subcategories' => $category->subcategories->map(function($subcategory) {
                            return [
                                'id' => $subcategory->id,
                                'name' => $subcategory->name
                            ];
                        })
                    ];
                });
            }

            return response()->json([
                'status' => '1',
                'data' => [
                    'categories' => $formattedCategories,
                    'payment_methods' => ['Cash', 'QR', 'Bank Transfer', 'Cheque', 'Credit', 'Debit', 'Other']
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController index: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Store a new money out record
     */
    public function store(Request $request)
    {
        try {
            $user = getUser();
            
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|uuid|exists:money_out_categories,id',
                'subcategory_id' => 'nullable|uuid|exists:money_out_subcategories,id',
                'description' => 'required',
                'price' => 'required|numeric|min:0',
                'payment_method' => 'required|in:Cash,QR,Bank Transfer,Cheque,Credit,Debit,Other',
                'record_date' => 'required|date',
                'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => '0',
                    'message' => $validator->errors()->first()
                ]);
            }

            // Get the category to determine the type
            $category = MoneyOutCategory::where('company_id', $user['company']['id'])
                ->where('id', $request->category_id)
                ->firstOrFail();

            // Validate subcategory belongs to the selected category if provided
            if (!empty($request->subcategory_id)) {
                $subcategory = MoneyOutSubcategory::where('id', $request->subcategory_id)
                    ->where('category_id', $request->category_id)
                    ->where('company_id', $user['company']['id'])
                    ->first();

                if (!$subcategory) {
                    return response()->json([
                        'status' => '0',
                        'message' => 'Selected subcategory does not belong to the selected category'
                    ]);
                }
            }

            $data = $request->except('thumbnail');
            $data['type'] = $category->type;
            $data['company_id'] = $user['company']['id'];
            $data['user_id'] = $user['id'];

            if ($request->hasFile('thumbnail')) {
                $companyId = $user['company']['id'];
                $userId = $user['id'];
                $path = $request->file('thumbnail')->store("money-out/receipts/{$companyId}/{$userId}", 's3');
                $data['thumbnail'] = $path;
            }

            $record = MoneyOutRecord::create($data);

            return response()->json([
                'status' => '1',
                'message' => "Money out record {$record->money_out_id} has been added successfully",
                'data' => $record->load(['category', 'subcategory'])
            ]);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController store: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Update a money out record
     */
    public function update(Request $request, $id)
    {
        try {
            $user = getUser();
            
            $record = MoneyOutRecord::where('id', $id)
                ->where('user_id', $user['id'])
                ->where('company_id', $user['company']['id'])
                ->first();

            if (!$record) {
                return response()->json([
                    'status' => '0',
                    'message' => 'Record not found'
                ]);
            }

            $validator = Validator::make($request->all(), [
                'description' => 'sometimes|string',
                'price' => 'sometimes|numeric|min:0',
                'category_id' => 'sometimes|uuid|exists:money_out_categories,id',
                'subcategory_id' => 'nullable|uuid|exists:money_out_subcategories,id',
                'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                'record_date' => 'sometimes|date'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => '0',
                    'message' => $validator->errors()->first()
                ]);
            }

            $data = $validator->validated();

            // If category_id is provided, update the type
            if (isset($data['category_id'])) {
                $category = MoneyOutCategory::where('company_id', $user['company']['id'])
                    ->where('id', $data['category_id'])
                    ->firstOrFail();
                $data['type'] = $category->type;

                // Clear subcategory if category changes
                if ($data['category_id'] !== $record->category_id) {
                    $data['subcategory_id'] = null;
                }
            }

            // Validate subcategory belongs to the selected category if provided
            if (!empty($data['subcategory_id'])) {
                $subcategory = MoneyOutSubcategory::where('id', $data['subcategory_id'])
                    ->where('category_id', $data['category_id'] ?? $record->category_id)
                    ->first();

                if (!$subcategory) {
                    return response()->json([
                        'status' => '0',
                        'message' => 'Selected subcategory does not belong to the selected category'
                    ]);
                }
            }

            // Handle thumbnail upload if provided
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if exists
                if ($record->thumbnail) {
                    Storage::disk('s3')->delete($record->thumbnail);
                }
                $companyId = $user['company']['id'];
                $userId = $user['id'];
                $path = $request->file('thumbnail')->store("money-out/receipts/{$companyId}/{$userId}", 's3');
                $data['thumbnail'] = $path;
            }

            $record->update($data);

            return response()->json([
                'status' => '1',
                'message' => 'Record updated successfully',
                'data' => $record->load(['category', 'subcategory'])
            ]);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController update: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Delete a money out record
     */
    public function destroy($id)
    {
        try {
            $user = getUser();
            
            $record = MoneyOutRecord::where('id', $id)
                ->where('user_id', $user['id'])
                ->where('company_id', $user['company']['id'])
                ->first();

            if (!$record) {
                return response()->json([
                    'status' => '0',
                    'message' => 'Record not found'
                ]);
            }

            // Delete thumbnail if exists
            if ($record->thumbnail) {
                Storage::disk('s3')->delete($record->thumbnail);
            }

            $record->delete();

            return response()->json([
                'status' => '1',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController destroy: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get profit and loss report
     */
    public function profitAndLoss(Request $request)
    {
        try {
            $user = getUser();
            
            if ($user['userRole'] === 'Staff') {
                return response()->json([
                    'status' => '1',
                    'data' => [
                        'sale' => '0.00',
                        'cost' => [
                            'total' => '0.00',
                            'items' => []
                        ],
                        'expense' => [
                            'total' => '0.00',
                            'items' => []
                        ],
                        'gross_profit' => '0.00',
                        'net_profit' => '0.00'
                    ]
                ]);
            }

            // Set default date range to current month if no parameters provided
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();

            // Handle date range parameters
            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->start_date);
                $endDate = Carbon::parse($request->end_date);
            } else if ($request->has('year_month')) {
                // Handle both formats: 2025_06 and 2025-06 
                $yearMonth = str_replace('_', '-', $request->year_month);
                $startDate = Carbon::parse($yearMonth)->startOfMonth()->startOfDay();
                $endDate = Carbon::parse($yearMonth)->endOfMonth()->endOfDay();

                // If the date range is within the current month, limit the end date to today
                if ($yearMonth === Carbon::today()->format('Y-m')) {
                    $endDate = Carbon::today()->endOfDay();
                }
            } else if ($request->has('year')) {
                $startDate = Carbon::createFromDate($request->year)->startOfYear()->startOfDay();
                $endDate = Carbon::createFromDate($request->year)->endOfYear()->endOfDay();
                
                // If the year is current year, limit the end date to today
                if ($request->year === Carbon::today()->format('Y')) {
                    $endDate = Carbon::today()->endOfDay();
                }
            }

            // Get total sales for the period
            $sales = \App\Models\Order::where('company_id', $user['company']['id'])
                ->whereBetween('order_date', [$startDate, $endDate])
                ->sum('grandtotal_decimal');

            // Get costs and expenses with category and subcategory relationships
            $moneyOutQuery = MoneyOutRecord::with(['category', 'subcategory'])
                ->where('company_id', $user['company']['id'])
                ->whereBetween('record_date', [$startDate, $endDate]);

            // Get all records grouped by type
            $records = $moneyOutQuery->get()->groupBy('type');

            // Initialize response structure
            $response = [
                'status' => '1',
                'data' => [
                    'sale' => number_format($sales, 2, '.', ''),
                    'cost' => [
                        'total' => 0,
                        'items' => []
                    ],
                    'expense' => [
                        'total' => 0,
                        'items' => []
                    ],
                    'gross_profit' => 0,
                    'net_profit' => 0
                ]
            ];

            // Process COGS (Cost of Goods Sold)
            if (isset($records['COGS'])) {
                $costRecords = $records['COGS']->groupBy(function($record) {
                    return $record->category->name;
                });
                foreach ($costRecords as $categoryName => $items) {
                    $categoryTotal = $items->sum('price');
                    $categoryItem = [
                        'name' => $categoryName,
                        'amount' => number_format($categoryTotal, 2, '.', ''),
                        'subcategories' => []
                    ];

                    // Group items by subcategory
                    $subcategoryGroups = $items->groupBy(function($record) {
                        return $record->subcategory ? $record->subcategory->name : 'Uncategorized';
                    });

                    foreach ($subcategoryGroups as $subcategoryName => $subcategoryItems) {
                        $subcategoryTotal = $subcategoryItems->sum('price');
                        $categoryItem['subcategories'][] = [
                            'name' => $subcategoryName,
                            'amount' => number_format($subcategoryTotal, 2, '.', '')
                        ];
                    }

                    $response['data']['cost']['items'][] = $categoryItem;
                    $response['data']['cost']['total'] += $categoryTotal;
                }
            }
            $response['data']['cost']['total'] = number_format($response['data']['cost']['total'], 2, '.', '');

            // Process Expenses
            if (isset($records['EXPENSES'])) {
                $expenseRecords = $records['EXPENSES']->groupBy(function($record) {
                    return $record->category->name;
                });
                foreach ($expenseRecords as $categoryName => $items) {
                    $categoryTotal = $items->sum('price');
                    $categoryItem = [
                        'name' => $categoryName,
                        'amount' => number_format($categoryTotal, 2, '.', ''),
                        'subcategories' => []
                    ];

                    // Group items by subcategory
                    $subcategoryGroups = $items->groupBy(function($record) {
                        return $record->subcategory ? $record->subcategory->name : 'Uncategorized';
                    });

                    foreach ($subcategoryGroups as $subcategoryName => $subcategoryItems) {
                        $subcategoryTotal = $subcategoryItems->sum('price');
                        $categoryItem['subcategories'][] = [
                            'name' => $subcategoryName,
                            'amount' => number_format($subcategoryTotal, 2, '.', '')
                        ];
                    }

                    $response['data']['expense']['items'][] = $categoryItem;
                    $response['data']['expense']['total'] += $categoryTotal;
                }
            }
            $response['data']['expense']['total'] = number_format($response['data']['expense']['total'], 2, '.', '');

            // Calculate profits
            $grossProfit = $sales - floatval(str_replace(',', '', $response['data']['cost']['total']));
            $netProfit = $grossProfit - floatval(str_replace(',', '', $response['data']['expense']['total']));

            $response['data']['gross_profit'] = number_format($grossProfit, 2, '.', '');
            $response['data']['net_profit'] = number_format($netProfit, 2, '.', '');

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController profitAndLoss: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get daily summary of money out records
     * Similar to report/overview but for money out instead of sales
     */
    public function dailySummary(Request $request)
    {
        $user = getUser();
        
        try {
            // Set default date range to current month
            $startDate = Carbon::now()->startOfMonth();
            $endDate   = Carbon::now()->endOfMonth();
            
            // Handle date range parameters (same logic as ReportController)
            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->start_date);
                $endDate   = Carbon::parse($request->end_date);
            } else if ($request->has('year_month')) {
                $startDate = Carbon::parse($request->year_month)->startOfMonth()->startOfDay();
                $endDate   = Carbon::parse($request->year_month)->endOfMonth()->endOfDay();
            }
            
            // Always limit end date to today if it's in the future
            if ($endDate->isAfter(Carbon::today())) {
                $endDate = Carbon::today();
            }

            // Generate array of all dates in range
            $dateRange = collect();
            $currentDate = $startDate->copy();

            while ($currentDate->lte($endDate)) {
                $dateRange->push($currentDate->format('d/m/Y'));
                $currentDate->addDay();
            }
            // Reverse the date range for descending order
            $dateRange = $dateRange->reverse()->values();
            
            // Get actual money out data grouped by date
            $moneyOutQuery = MoneyOutRecord::where('company_id', $user['company']['id']);
            
            // For staff users, filter by user_id; for others, get company-wide data
            if ($user['userRole'] === 'Staff') {
                $moneyOutQuery->where('user_id', $user['id']);
            }
            
            $moneyOut = $moneyOutQuery->whereBetween('record_date', [$startDate, $endDate])
                ->selectRaw('DATE_FORMAT(record_date, "%d/%m/%Y") as date, SUM(price) as daily_money_out')
                ->groupBy('date')
                ->orderBy('date', 'DESC')
                ->pluck('daily_money_out', 'date');

            // Map the date range to include zeros for missing dates
            $dailyMoneyOut = $dateRange->map(function ($date) use ($moneyOut) {
                return [
                    'date'             => $date,
                    'daily_money_out'  => number_format($moneyOut[$date] ?? 0, 2, '.', ',')
                ];
            })->values();

            // For staff users, return zeros for totals but keep daily details
            if ($user['userRole'] === 'Staff') {
                return response()->json([
                    'status' => '1',
                    'total' => '0.00',
                    'compare_last_month' => [
                        'last_month_total' => '0.00',
                        'percentage_change' => '0.00',
                        'amount_change' => '0.00'
                    ],
                    'daily_money_out' => $dailyMoneyOut
                ]);
            }

            // Calculate previous month's date range
            $previousStartDate = $startDate->copy()->subMonth()->startOfMonth();
            $previousEndDate = $startDate->copy()->subMonth()->endOfMonth();

            // Get previous month's total
            $previousMonthQuery = MoneyOutRecord::where('company_id', $user['company']['id']);
            
            // For staff users, filter by user_id; for others, get company-wide data
            if ($user['userRole'] === 'Staff') {
                $previousMonthQuery->where('user_id', $user['id']);
            }
            
            $previousMonthTotal = $previousMonthQuery->whereBetween('record_date', [$previousStartDate, $previousEndDate])
                ->sum('price');
            
            // Calculate total money out for the period
            $totalMoneyOut = $moneyOut->sum();
            if (!$totalMoneyOut) {
                $totalMoneyOut = 0;
            }

            // Calculate comparison metrics
            $amountChange = $totalMoneyOut - $previousMonthTotal;
            $percentageChange = $previousMonthTotal > 0 ? 
                (($totalMoneyOut - $previousMonthTotal) / $previousMonthTotal) * 100 : 
                0;
                     
            return response()->json([
                'status'           => '1',
                'total'            => number_format($totalMoneyOut, 2, '.', ','),
                'compare_last_month' => [
                    'last_month_total' => number_format($previousMonthTotal, 2, '.', ','),
                    'percentage_change' => number_format($percentageChange, 2, '.', ','),
                    'amount_change' => number_format($amountChange, 2, '.', ',')
                ],
                'daily_money_out'  => $dailyMoneyOut
            ]);
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController dailySummary: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Get daily transaction details for money out records
     * Shows individual records for a specific date
     */
    public function dailyTransactions(Request $request)
    {
        $date = $request->date;
        if (!$date) {
            return response()->json(['status' => '0', 'message' => 'Invalid date']);
        }

        $user = getUser();

        try {
            // Convert date from d/m/Y format to Y-m-d format for database query
            $targetDate = Carbon::createFromFormat('d/m/Y', $date)->format('Y-m-d');
            
            // Get all money out records for the specific date
            $recordsQuery = MoneyOutRecord::with(['category', 'subcategory'])
                ->where('company_id', $user['company']['id']);
            
            // For staff users, filter by user_id; for others, get company-wide data
            if ($user['userRole'] === 'Staff') {
                $recordsQuery->where('user_id', $user['id']);
            }
            
            $records = $recordsQuery->whereDate('record_date', $targetDate)
                ->orderBy('created_at', 'desc')
                ->get();

            // Format the response similar to the image structure
            $transactions = $records->map(function ($record) {
                return [
                    'id' => $record->id,
                    'money_out_id' => $record->money_out_id,
                    'description' => $record->description,
                    'category_name' => $record->category ? $record->category->name : 'Unknown',
                    'subcategory_name' => $record->subcategory ? $record->subcategory->name : null,
                    'type' => $record->type, // COGS or EXPENSES
                    'amount' => number_format($record->price, 2, '.', ''),
                    'date' => $record->record_date->format('d/m'),
                    'full_date' => $record->record_date->format('d/m/Y'),
                    'payment_method' => $record->payment_method,
                    'thumbnail' => $record->thumbnail ? url("api/v3/report/money-out/{$record->id}/receipt") : null,
                    'created_at' => $record->created_at->format('H:i')
                ];
            });

            // Calculate total for the day
            $dailyTotal = $records->sum('price');

            return response()->json([
                'status' => '1',
                'date' => $date,
                'money_out_total' => number_format($dailyTotal, 2, '.', ''),
                'transaction_count' => $records->count(),
                'transactions' => $transactions
            ]);

        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController dailyTransactions: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Serve secure receipt file directly for API access
     */
    public function serveReceipt($id)
    {
        try {
            $user = getUser();
            
            $recordQuery = MoneyOutRecord::where('company_id', $user['company']['id']);
            
            // For staff users, filter by user_id; for others, get company-wide data
            if ($user['userRole'] === 'Staff') {
                $recordQuery->where('user_id', $user['id']);
            }
            
            $record = $recordQuery->findOrFail($id);

            if (!$record->thumbnail) {
                abort(404, 'Receipt not found');
            }

            // Check if file exists in S3
            if (!Storage::disk('s3')->exists($record->thumbnail)) {
                abort(404, 'Receipt file not found');
            }

            // Get file content from S3
            $fileContent = Storage::disk('s3')->get($record->thumbnail);
            
            // Get file mime type
            $mimeType = Storage::disk('s3')->mimeType($record->thumbnail);
            
            // Return file content with proper headers
            return response($fileContent)
                ->header('Content-Type', $mimeType)
                ->header('Content-Disposition', 'inline')
                ->header('Cache-Control', 'private, max-age=300'); // Cache for 5 minutes
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController serveReceipt: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()], 404);
        }
    }

    /**
     * Generate and download profit and loss report as PDF
     */
    public function downloadProfitLossPDF(Request $request)
    {
        try {
            $user = getUser();
            
            if ($user['userRole'] === 'Staff') {
                return response()->json([
                    'status'  => '0',
                    'message' => 'Access denied for staff users',
                ]);
            }

            // Set default date range to current month if no parameters provided
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
            $periodLabel = Carbon::now()->format('F Y');

            // Handle date range parameters
            if ($request->has('start_date') && $request->has('end_date')) {
                $startDate = Carbon::parse($request->start_date);
                $endDate = Carbon::parse($request->end_date);
                $periodLabel = $startDate->format('F Y');
                if ($startDate->format('Y-m') !== $endDate->format('Y-m')) {
                    $periodLabel = $startDate->format('M Y') . ' - ' . $endDate->format('M Y');
                }
            } else if ($request->has('year_month')) {
                // Handle both formats: 2025_06 and 2025-06 
                $yearMonth = str_replace('_', '-', $request->year_month);
                $startDate = Carbon::parse($yearMonth)->startOfMonth()->startOfDay();
                $endDate = Carbon::parse($yearMonth)->endOfMonth()->endOfDay();
                $periodLabel = Carbon::parse($yearMonth)->format('F Y');

                // If the date range is within the current month, limit the end date to today
                if ($yearMonth === Carbon::today()->format('Y-m')) {
                    $endDate = Carbon::today()->endOfDay();
                }
            } else if ($request->has('year')) {
                $startDate = Carbon::createFromDate($request->year)->startOfYear()->startOfDay();
                $endDate = Carbon::createFromDate($request->year)->endOfYear()->endOfDay();
                $periodLabel = $request->year;
                
                // If the year is current year, limit the end date to today
                if ($request->year === Carbon::today()->format('Y')) {
                    $endDate = Carbon::today()->endOfDay();
                }
            }

            // Get total sales for the period
            $sales = \App\Models\Order::where('company_id', $user['company']['id'])
                ->whereBetween('order_date', [$startDate, $endDate])
                ->sum('grandtotal_decimal');

            // Get costs and expenses with category and subcategory relationships
            $moneyOutQuery = MoneyOutRecord::with(['category', 'subcategory'])
                ->where('company_id', $user['company']['id'])
                ->whereBetween('record_date', [$startDate, $endDate]);

            // Get all records grouped by type
            $records = $moneyOutQuery->get()->groupBy('type');

            // Initialize data structure
            $data = [
                'sale' => $sales,
                'cost' => [
                    'total' => 0,
                    'items' => []
                ],
                'expense' => [
                    'total' => 0,
                    'items' => []
                ],
                'gross_profit' => 0,
                'net_profit' => 0
            ];

            // Process COGS (Cost of Goods Sold)
            if (isset($records['COGS'])) {
                $costRecords = $records['COGS']->groupBy(function($record) {
                    return $record->category->name;
                });
                foreach ($costRecords as $categoryName => $items) {
                    $categoryTotal = $items->sum('price');
                    $categoryItem = [
                        'name' => $categoryName,
                        'amount' => $categoryTotal,
                        'subcategories' => []
                    ];

                    // Group items by subcategory
                    $subcategoryGroups = $items->groupBy(function($record) {
                        return $record->subcategory ? $record->subcategory->name : 'Uncategorized';
                    });

                    foreach ($subcategoryGroups as $subcategoryName => $subcategoryItems) {
                        $subcategoryTotal = $subcategoryItems->sum('price');
                        $categoryItem['subcategories'][] = [
                            'name' => $subcategoryName,
                            'amount' => $subcategoryTotal
                        ];
                    }

                    $data['cost']['items'][] = $categoryItem;
                    $data['cost']['total'] += $categoryTotal;
                }
            }

            // Process Expenses
            if (isset($records['EXPENSES'])) {
                $expenseRecords = $records['EXPENSES']->groupBy(function($record) {
                    return $record->category->name;
                });
                foreach ($expenseRecords as $categoryName => $items) {
                    $categoryTotal = $items->sum('price');
                    $categoryItem = [
                        'name' => $categoryName,
                        'amount' => $categoryTotal,
                        'subcategories' => []
                    ];

                    // Group items by subcategory
                    $subcategoryGroups = $items->groupBy(function($record) {
                        return $record->subcategory ? $record->subcategory->name : 'Uncategorized';
                    });

                    foreach ($subcategoryGroups as $subcategoryName => $subcategoryItems) {
                        $subcategoryTotal = $subcategoryItems->sum('price');
                        $categoryItem['subcategories'][] = [
                            'name' => $subcategoryName,
                            'amount' => $subcategoryTotal
                        ];
                    }

                    $data['expense']['items'][] = $categoryItem;
                    $data['expense']['total'] += $categoryTotal;
                }
            }

            // Calculate profits
            $data['gross_profit'] = $data['sale'] - $data['cost']['total'];
            $data['net_profit'] = $data['gross_profit'] - $data['expense']['total'];

            // Get company information
            $company = Company::find($user['company']['id']);
            
            // Check for company logo from Receipt model (S3 storage)
            $companyLogo = null;
            if ($company) {
                $receipt = Receipt::where('company_id', $company->id)->first();
                if ($receipt && $receipt->logo) {
                    try {
                        $logoContent = Storage::disk('s3')->get($receipt->logo);
                        $companyLogo = 'data:image/' . pathinfo($receipt->logo, PATHINFO_EXTENSION) . ';base64,' . base64_encode($logoContent);
                    } catch (\Exception $e) {
                        Log::warning("Failed to load company logo from S3: {$e->getMessage()}", [
                            'logo_path' => $receipt->logo,
                            'company_id' => $company->id
                        ]);
                    }
                }
            }

            // Prepare data for PDF
            $pdfData = [
                'company' => $company,
                'companyLogo' => $companyLogo,
                'user' => $user,
                'periodLabel' => $periodLabel,
                'startDate' => $startDate->format('d/m/Y'),
                'endDate' => $endDate->format('d/m/Y'),
                'profitLoss' => $data,
                'generatedAt' => Carbon::now()->format('d/m/Y H:i:s'),
            ];
            
            // Generate PDF
            $pdf = Pdf::loadView('backend.report.profit-loss-pdf', $pdfData)
                ->setPaper('A4', 'portrait')
                ->setOptions([
                    'dpi' => 150,
                    'defaultFont' => 'sans-serif',
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => true,
                ]);
            
            $filename = 'profit_loss_' . str_replace(['/', ' '], ['_', '_'], $periodLabel) . '.pdf';
            
            return $pdf->download($filename);
            
        } catch (\Exception $e) {
            Log::error('Error in MoneyOutController downloadProfitLossPDF: ' . $e->getMessage());
            return response()->json(['status' => '0', 'message' => $e->getMessage()]);
        }
    }
} 