<?php

namespace App\Http\Controllers\auth;

use App\Models\User;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\UserDetail;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Mail\WelcomeTrialUserSimple;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;

class SimpleTrialRegistrationController extends Controller
{
    /**
     * Show the registration form.
     *
     * @return \Illuminate\View\View
     */
    public function showRegisterForm()
    {
        return view("simple_event_trial_register");
    }

    /**
     * Handle the registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function register(Request $request)
    {
        $request->validate([
            "full_name" => ["required", "string", "max:255"],
            "email" => [
                "required",
                "string",
                "email",
                "max:255",
                "unique:users",
            ],
            "mobile" => ["required", "string", "min:10", "max:11"],
        ]);

        // Split full name into first and last name
        $nameParts = explode(" ", $request->full_name, 2);
        $firstName = $nameParts[0];
        $lastName = isset($nameParts[1]) ? $nameParts[1] : "";

        $temp_password = Str::random(12);

        $user = User::create([
            "username" => $request->email,
            "email" => $request->email,
            "password" => $temp_password,
            "email_verified_at" => now(),
            "onboarding_complete" => false,
            "isBizappUser" => "N",
            "access_module" => "connected",
        ]);

        // Create user details
        $userDetail = UserDetail::create([
            "user_id" => $user->id,
            "first_name" => $firstName,
            "last_name" => $lastName,
            "mobile" => $request->mobile,
            "address" => "",
            "city" => "",
            "state" => "1",
            "postcode" => "",
            "country" => "MALAYSIA",
        ]);

        \Log::info("Trial registration completed", [
            "user_id" => $user->id,
            "email" => $user->email,
        ]);

        try {
            // Send welcome email
            Mail::to($user->email)->send(
                new WelcomeTrialUserSimple($user, $temp_password)
            );
        } catch (\Exception $e) {
            \Log::error("Failed to send welcome email: " . $e->getMessage());
            // Continue with registration even if email fails
        }

        // Log the user in and redirect to dashboard
        Auth::login($user);

        return redirect()
            ->route("dashboard")
            ->with(
                "success",
                "Registration successful! Welcome to your trial account. Please check your email for login credentials."
            );
    }

    /**
     * Show the onboarding form content for modal.
     *
     * @return \Illuminate\View\View
     */
    public function showOnboardingForm()
    {
        return view("backend.onboarding_dashboard");
    }

    /**
     * Handle the onboarding completion request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function completeOnboarding(Request $request)
    {
        try {
            $request->validate([
                "first_name" => ["required", "string", "max:255"],
                "last_name" => ["required", "string", "max:255"],
                "password" => ["required", "string", "min:4", "confirmed"],
                "mobile" => ["required", "string", "max:14"],
            ]);

            $user = Auth::user();

            $user->update([
                "password" => $request->password,
                "onboarding_complete" => true,
            ]);

            // Create user details
            $userDetail = $user->userDetails()->update([
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "mobile" => $request->mobile,
            ]);

            $company = Company::create([
                "user_id" => $user->id,
                "com_name" => $request->first_name . " " . $request->last_name,
                "com_address" => "address",
                "com_city" => "city",
                "com_state" => "1",
                "com_postcode" => "0",
                "com_country" => "MALAYSIA",
                "com_registration_no" => null,
                "com_mobile" => $request->mobile,
                "com_email" => $request->email,
                "com_sst_value" => "0",
                "account_type" => "individual",
                "ic_number" => "0",
            ]);

            Receipt::create([
                "company_id" => $company->id,
                "user_id" => $user->id,
                "title" => $company->com_name,
                "name" => $request->first_name . " " . $request->last_name,
                "email" => $request->email,
                "phone" => $request->mobile,
                "address" => "address",
                "city" => "city",
                "state" => "1",
                "postcode" => "0",
                "country" => "MALAYSIA",
                "sst" => "0,0",
            ]);

            // Find the trial plan
            $trialPlan = SubscriptionPlan::where("name", "Starter - Trial")
                ->where("is_active", true)
                ->first();

            if (!$trialPlan) {
                throw new \Exception("Trial plan not found");
            }

            // Create trial subscription
            $subscription = Subscription::create([
                "company_id" => $company->id,
                "subscription_plan_id" => $trialPlan->id,
                "starts_at" => now(),
                "ends_at" => now()->addDays(7),
                "trial_ends_at" => now()->addDays(7),
                "status" => "active",
                "bill_type" => "trial",
            ]);

            if ($request->ajax()) {
                return response()->json([
                    "success" => true,
                    "message" =>
                        "Your account setup is complete! Welcome to the dashboard.",
                ]);
            }

            if ($request->ajax()) {
                return response()->json([
                    "success" => true,
                    "message" =>
                        "Your account setup is complete! Welcome to the dashboard.",
                    "redirect" => route("dashboard"),
                ]);
            }

            return redirect()
                ->route("dashboard")
                ->with(
                    "success",
                    "Your account setup is complete! Welcome to the dashboard."
                );
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json(
                    [
                        "success" => false,
                        "message" => $e->getMessage(),
                    ],
                    422
                );
            }

            if ($request->ajax()) {
                return response()->json(
                    [
                        "success" => false,
                        "message" => $e->getMessage(),
                    ],
                    422
                );
            }

            return back()
                ->withErrors(["error" => $e->getMessage()])
                ->withInput();
        }
    }
}
